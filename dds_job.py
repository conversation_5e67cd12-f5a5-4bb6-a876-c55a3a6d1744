from service.dds_service import DDSService
from model.job import Job
from datetime import datetime

from common.log_utils import log
from service.web_service import WebService
from service.parse_service import ParseService
from argparse import ArgumentParser
import time
from common.config_utils import get_json_config_by_jstr
from common.date_utils import get_date, get_date_formatted
import base64
from common.config_utils import get_config_by_file
import json



# 间隔 1小时

def get_saved_configuration_name(job_index: int,e_year, e_month, e_day):
    """ 提交任务名称 """
    return f'{job_index}_{datetime.now().strftime("%Y%m%d_%H%M%S%f")[:-3]}'

def get_csv_file_name(job_index: int,s_year, s_month, s_day):
    """ 下载任务名称 """
    return f'{job_index}_{datetime(s_year, s_month, s_day).strftime("%Y%m%d")}'

def main(base64_json_str, s_year, s_month, s_day, e_year, e_month, e_day, job_index, common_json_str):
    log.info(f'base64_json_str: {base64_json_str}')
    json_str = base64.b64decode(base64_json_str).decode("utf-8")
    if common_json_str:
        common_json = json.loads(common_json_str)[0]
    
    parse_service = ParseService(common_json)
    dds_agent = DDSService(common_json)    
    # if dds_agent.login_invalid():
    #     # 登录
    #     WebService(common_json).login()
    
    job = Job()
    
    replacements = {
         "${period_s_date}": get_date(s_year, s_month, s_day)
        ,"${period_s_date_formatted}": get_date_formatted(s_year, s_month, s_day)
        ,"${period_e_date}": get_date(e_year, e_month, e_day)
        ,"${period_e_date_formatted}": get_date_formatted(e_year, e_month, e_day)
        ,"${period_se_date_formatted}": f'{get_date_formatted(s_year, s_month, s_day)} to {get_date_formatted(e_year, e_month, e_day)}'
    }
    job.saved_configuration = base64_json_str
    saved_configuration = get_json_config_by_jstr(json_str, replacements=replacements)
    # 轮训任务时间
    max_polling_time_out = common_json['max_polling_time_out']
    # 任务状态循环监测时间
    loop_interval = common_json['loop_interval']
    job_success = common_json['job_success']
        
    # 下载文件名
    job.csv_file_name = get_csv_file_name(job_index, s_year, s_month, s_day)
    # 提交报表名
    job.saved_configuration_name = get_saved_configuration_name(job_index, e_year, e_month, e_day)
    job = dds_agent.submit(job, saved_configuration)
    
    start_time = time.time()
    while True:
        job = dds_agent.get_job_status(job)
        
        log.info(f'get job status {job}')
        if job is not None and job.status == job_success and job.job_id is not None:
            log.info(f'job {job.saved_configuration_name} 已经准备就绪....')
            break
        
        
        if job is not None and job.status == 'failed' and job.job_id is not None:
            log.error(f'job {job.saved_configuration_name} 任务状态为: {job.status}，请检查具体原因....')
            raise Exception(f'job {job.saved_configuration_name} 任务状态为: {job.status}，请检查具体原因....')
        
        # 检查是否超时
        if time.time() - start_time > max_polling_time_out:
            log.error(f'job {job.saved_configuration_name} 轮询超时，超过 {max_polling_time_out/60} 分钟')
            raise TimeoutError(f'Job polling timeout after {max_polling_time_out/60} minutes')
        
        log.info(f'job {job.saved_configuration_name} 未就绪，sleep {loop_interval}s')
        time.sleep(loop_interval)
    
    file_name = dds_agent.download_job(job)
    
    if file_name is None:
        log.error(f'job {job} 下载失败')
        raise Exception(f'Job 下载失败， 获取不到下载后的文件名 {file_name}')
    
    log.debug(f'开始解析文件： {file_name} ')
    parsed_file_name, params_file = parse_service.parse_csv(file_name, base64_json_str)
    log.debug(f'解析完成，文件名为： {parsed_file_name}, {params_file} ')
    doris_table_name = common_json['doris_table_name']
    doris_table_columns = common_json['doris_table_columns']
    parse_service.write2doris(parsed_file_name, doris_table_name, doris_table_columns)
    
    log.debug(f'写入数据成功 {parsed_file_name} ')
    
    doris_table_name_param = common_json['doris_table_name_param']
    doris_table_columns_param = common_json['doris_table_columns_param']
    parse_service.write2doris(params_file,doris_table_name_param , doris_table_columns_param)
    
    log.debug(f'写入数据成功 {params_file} ')
    
    
def local_test_run():
    s_year, s_month, s_day = 2025, 1, 13
    e_year, e_month, e_day = 2025, 1, 15
    job_indexs = {
        # 12:'中国大陆-中国港澳台'
        # ,13:'中国大陆-日本'
        # ,14:'中国大陆-韩国'
        # ,15:'中国大陆-东南亚'
        # ,16:'中国大陆-其他国家'
        # ,17:'中国大陆-欧洲'
        # ,18:'中国大陆-澳洲'
        # ,19:'中国大陆-除亚欧澳之外'
        # ,21:'中国港澳台-中国大陆'
        # ,22:'中国港澳台-中国港澳台'
        # ,23:'中国港澳台-日本'
        # ,24:'中国港澳台-韩国'
        # ,25:'中国港澳台-东南亚'
        # ,26:'中国港澳台-其他国家'
        # 27:'中国港澳台-欧洲'
        # ,28:'中国港澳台-澳洲'
        # ,29:'中国港澳台-除亚欧澳之外'
        # ,31:'日本-中国大陆'
        # ,32:'日本-中国港澳台'
        37:'日本-欧洲'
        # ,41:'韩国-中国大陆'
        # ,42:'韩国-中国港澳台'
        # ,43:'韩国-欧洲'
        # ,51:'东南亚-中国大陆'
        # ,52:'东南亚-中国港澳台'
        # ,61:'其他国家-中国大陆'
        # ,62:'其他国家-中国港澳台'
        # ,71:'欧洲-中国大陆'
        # ,72:'欧洲-中国港澳台'
        # ,73:'欧洲-日本'
        # ,74:'欧洲-韩国'
        # ,78:'欧洲-澳洲'
        # ,81:'澳洲-中国大陆'
        # ,82:'澳洲-中国港澳台'
        # ,87:'澳洲-欧洲'
        # ,91:'除亚欧澳之外-中国大陆'
        # ,92:'除亚欧澳之外-中国港澳台'
        }
    
    for key, value in job_indexs.items():
    
        log.info(f'开始处理{value}任务')
        #main(s_year, s_month, s_day, e_year, e_month, e_day, key)
        
        config_name = f'mda/dds/config/report_sample_{key}.json'
        saved_configuration = get_config_by_file(s_year, s_month, s_day, e_year, e_month, e_day, config_name)
        
        import base64
        base64_json_str = base64.b64encode(saved_configuration.encode('utf-8')).decode('utf-8')
        
        common_json_str = """
[
    {
        "max_polling_time_out":3600
        ,"submit_url":"dds-reports/api/request-report"
        ,"get_job_report_url":"system-management/management/systems/user-conveniences/report-histories"
        ,"download_url":"user-conveniences/api/user-conveniences/download/report-histories"
        ,"project_base_path": "/Users/<USER>/workspace/vscode/dds"
        ,"downloads_path": "downloads"
        ,"n_token_flash_second": 3600
        ,"loop_interval":5
        ,"job_success" : "success"
        ,"login_max_retry_count": 3
        ,"login_retry_interval_second": 5
        ,"chrome_runtime_mem": 4096
        ,"selenium_operate_max_timeout_second": 60
        ,"selenium_step2step_interval_second": 1
        ,"remote_debugging_port" : 9222
        ,"max_connect_timeout_second": 30
        ,"max_read_timeout_second": 2900
        ,"doris_table_name": "ods_mkt_dds_sales_monitoring"
        ,"doris_table_columns": "id,index_id,uuid,trip_month,ticket_type,travel_agency_number,travel_agency_name,country_of_sale,source,gds,ticketing_ai,poo_airport,distribution_channel,transaction,od_dominant_cabin_class,od_rbkd,dominant_marketing_airline,marketing_airline_1,marketing_airline_2,marketing_airline_3,marketing_airline_4,marketing_airline_5,marketing_airline_6,dominant_operating_airline,operating_airline_1,operating_airline_2,operating_airline_3,operating_airline_4,operating_airline_5,operating_airline_6,origin,trip_origin_city,trip_origin_country_code,trip_origin_country_name,trip_origin_region,stop_1,stop_2,stop_3,stop_4,stop_5,stop_6,destination,trip_destination_city,trip_destination_country_code,trip_destination_country_name,trip_destination_region,non_stop_and_connecting_indicator,od_days_sold_prior_to_travel,duration_minutes,distance,pax,blended_average_fare,blended_revenue,blended_payment_amount,csv_file_name"
        ,"doris_table_name_param": "ods_mkt_dds_sales_monitoring_param"
        ,"doris_table_columns_param": "id,csv_file_name,saved_configuration"
    }
]
        """
        
        main(base64_json_str, s_year, s_month, s_day, e_year, e_month, e_day, key, common_json_str)

if __name__ == '__main__':
    # local_test_run()
    parser = ArgumentParser()
    parser.add_argument('json_str', help='任务启动json')
    parser.add_argument('start_year', help='开始年')
    parser.add_argument('start_month', help='开始月')
    parser.add_argument('start_day', help='开始日')
    parser.add_argument('end_year', help='结束年')
    parser.add_argument('end_month', help='结束月')
    parser.add_argument('end_day', help='结束日')
    parser.add_argument('run_day', help='运行日期')
    parser.add_argument('job_index', help='任务下标')
    parser.add_argument('common_json_str', help='公共配置任务')
    
    parser = parser.parse_args()
    json_str = parser.json_str
    job_index = parser.job_index
    
    start_year = parser.start_year
    start_month = parser.start_month
    start_day = parser.start_day
    end_year = parser.end_year
    end_month = parser.end_month
    end_day = parser.end_day
    run_day = parser.run_day
    common_json_str = base64.b64decode(parser.common_json_str).decode("utf-8")
    s_year = None
    s_month = None
    s_day = None
    e_year = None
    e_month = None
    e_day = None
    if start_year is not None and start_year != '':
        s_year = int(start_year)

    if start_month is not None and start_month != '':
        s_month = int(start_month)

    if start_day is not None and start_day != '':
        s_day = int(start_day)
        
    if end_year is not None and end_year != '':
        e_year = int(end_year)

    if end_month is not None and end_month != '':
        e_month = int(end_month)

    if end_day is not None and end_day != '':
        e_day = int(end_day)

    
    main(json_str, s_year, s_month, s_day, e_year, e_month, e_day, job_index, common_json_str)
    
    
    
    
   
    
    
    