import os
import pandas as pd
from common.doris_utils import stream_load 
from common.nacos_utils import get_config
from common.common_utils import settings 
from common.log_utils   import log
import duckdb
import io
from pathlib import Path


import base64

def encode_to_base64(text: str) -> str:
    """将字符串编码为base64"""
    return base64.b64encode(text.encode('utf-8')).decode('utf-8')

def decode_from_base64(base64_str: str) -> str:
    """将base64字符串解码为普通字符串"""
    return base64.b64decode(base64_str).decode('utf-8')

class ParseService:
    
    common_json = None
    download_path = NotImplementedError
    
    def __init__(self, common_json: dict):
        self.common_json = common_json
        self.download_path = common_json['project_base_path'] + '/' + common_json['downloads_path']
    
    def save_param(self, csv_file_name, saved_configuration):
        """ 保存参数到数据库 """
        df = pd.DataFrame({
            'csv_file_name': csv_file_name,
            'saved_configuration': saved_configuration
        }, index=[0])
        
        select_sql = """
        select 
            MD5(csv_file_name) as id,
            csv_file_name,
            saved_configuration
        from df
        """
        name_without_ext = os.path.splitext(csv_file_name)[0]
        processed_file_name = f"{name_without_ext}_param.csv"
        dist_file_full_path = os.path.join(self.download_path, processed_file_name)
        duckdb.sql(f"copy ({select_sql}) to '{dist_file_full_path}' (HEADER 'false', DELIMITER ',', NULLSTR '')")
        log.info(f'存储参数成功 {dist_file_full_path} success')
        
        return dist_file_full_path
    
    def parse_csv(self,csv_file_name, saved_configuration):
        """ 
            1. 拼接 csv_file_name和base_doiwnload_path 为 full_file_name
            2. 使用pandas 读取csv文件
            3. 数据清洗为写入数据库做准备
                - 去掉CSV前三行和CSV的最后一行
                - 重命名列:['trip_month','ticket_type','travel_agency_number','travel_agency_name','country_of_sale','source','gds','ticketing_ai','poo_airport','distribution_channel','transaction','od_dominant_cabin_class','od_rbkd','dominant_marketing_airline','marketing_airline_1','marketing_airline_2','marketing_airline_3','marketing_airline_4','marketing_airline_5','marketing_airline_6','dominant_operating_airline','operating_airline_1','operating_airline_2','operating_airline_3','operating_airline_4','operating_airline_5','operating_airline_6','origin','trip_origin_city','trip_origin_country_code','trip_origin_country_name','trip_origin_region','stop_1','stop_2','stop_3','stop_4','stop_5','stop_6','destination','trip_destination_city','trip_destination_country_code','trip_destination_country_name','trip_destination_region','non_stop_and_connecting_indicator','od_days_sold_prior_to_travel','duration_minutes','distance','pax','blended_average_fare','blended_revenue','blended_payment_amount'] 
                - 将数据持久化道磁盘，并且名称和原来名称进行区分，比如后缀加一个p
                
            4. 返回持久化后的文件
            
        """
        
        # 生成参数文件
        param_file = self.save_param(csv_file_name, saved_configuration)
        
        # 1. 拼接文件路径
        full_file_name = os.path.join(self.download_path, csv_file_name)
        
        # 2. 使用pandas读取CSV文件
        with open(full_file_name, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            
            # 移除每行末尾的逗号
            lines = [line.rstrip(',\n') + '\n' for line in lines]
            
        print(f"原始文件总行数: {len(lines)}")
        # 去除前13行和最后一行
        clean_lines = lines[13:-1]  # 跳过前13行，去除最后一行
        # 将清理后的数据转换为字符串
        clean_data = ''.join(clean_lines)
        # 使用StringIO创建一个类似文件的对象
        data_io = io.StringIO(clean_data)
        pd.set_option('future.no_silent_downcasting', True)
        df = pd.read_csv(data_io, quotechar='"', escapechar=None, dtype=str)
        df = df.replace(',', '', regex=True, )
        
        # 重命名列
        new_columns = ['trip_month','ticket_type','travel_agency_number','travel_agency_name','country_of_sale','source','gds','ticketing_ai','poo_airport','distribution_channel','transaction','od_dominant_cabin_class','od_rbkd','dominant_marketing_airline','marketing_airline_1','marketing_airline_2','marketing_airline_3','marketing_airline_4','marketing_airline_5','marketing_airline_6','dominant_operating_airline','operating_airline_1','operating_airline_2','operating_airline_3','operating_airline_4','operating_airline_5','operating_airline_6','origin','trip_origin_city','trip_origin_country_code','trip_origin_country_name','trip_origin_region','stop_1','stop_2','stop_3','stop_4','stop_5','stop_6','destination','trip_destination_city','trip_destination_country_code','trip_destination_country_name','trip_destination_region','non_stop_and_connecting_indicator','od_days_sold_prior_to_travel','duration_minutes','distance','pax','blended_average_fare','blended_revenue','blended_payment_amount']
        df.columns = new_columns
        
        # 将trip_month列的日期格式从yyyy-MM转换为yyyy-MM-dd
        df['trip_month'] = df['trip_month'].apply(lambda x: f"{x}-01" if pd.notna(x) else x)
        
        # 生成新文件名（添加p后缀）
        name_without_ext = os.path.splitext(csv_file_name)[0]
        processed_file_name = f"{name_without_ext}p.csv"
        dist_file_full_path = os.path.join(self.download_path, processed_file_name)
        
        # dataframe 新增一列index ,值为df.index 
        df['index_id'] = df.index
        df['csv_file_name'] = csv_file_name
        
        # 集成duckdb
        select_sql = """
select
MD5(concat(
 nullif(CAST(trip_month as string), '')
    ,nullif(CAST(ticket_type as string), '')
    ,nullif(CAST(travel_agency_number as string), '')
    ,nullif(CAST(travel_agency_name as string), '')
    ,nullif(CAST(country_of_sale as string), '')
    ,nullif(CAST(source as string), '')
    ,nullif(CAST(gds as string), '')
    ,nullif(CAST(ticketing_ai as string), '')
    ,nullif(CAST(poo_airport as string), '')
    ,nullif(CAST(distribution_channel as string), '')
    ,nullif(CAST(transaction as string), '')
    ,nullif(CAST(od_dominant_cabin_class as string), '')
    ,nullif(CAST(od_rbkd as string), '')
    ,nullif(CAST(dominant_marketing_airline as string), '')
    ,nullif(CAST(marketing_airline_1 as string), '')
    ,nullif(CAST(marketing_airline_2 as string), '')
    ,nullif(CAST(marketing_airline_3 as string), '')
    ,nullif(CAST(marketing_airline_4 as string), '')
    ,nullif(CAST(marketing_airline_5 as string), '')
    ,nullif(CAST(marketing_airline_6 as string), '')
    ,nullif(CAST(dominant_operating_airline as string), '')
    ,nullif(CAST(operating_airline_1 as string), '')
    ,nullif(CAST(operating_airline_2 as string), '')
    ,nullif(CAST(operating_airline_3 as string), '')
    ,nullif(CAST(operating_airline_4 as string), '')
    ,nullif(CAST(operating_airline_5 as string), '')
    ,nullif(CAST(operating_airline_6 as string), '')
    ,nullif(CAST(origin as string), '')
    ,nullif(CAST(trip_origin_city as string), '')
    ,nullif(CAST(trip_origin_country_code as string), '')
    ,nullif(CAST(trip_origin_country_name as string), '')
    ,nullif(CAST(trip_origin_region as string), '')
    ,nullif(CAST(stop_1 as string), '')
    ,nullif(CAST(stop_2 as string), '')
    ,nullif(CAST(stop_3 as string), '')
    ,nullif(CAST(stop_4 as string), '')
    ,nullif(CAST(stop_5 as string), '')
    ,nullif(CAST(stop_6 as string), '')
    ,nullif(CAST(destination as string), '')
    ,nullif(CAST(trip_destination_city as string), '')
    ,nullif(CAST(trip_destination_country_code as string), '')
    ,nullif(CAST(trip_destination_country_name as string), '')
    ,nullif(CAST(trip_destination_region as string), '')
    ,nullif(CAST(non_stop_and_connecting_indicator as string), '')
    ,nullif(CAST(od_days_sold_prior_to_travel as string), '')
    ,nullif(CAST(duration_minutes as string), '')
    ,nullif(CAST(distance as string), '')
    ,nullif(CAST(pax as string), '')
    ,nullif(CAST(blended_average_fare as string), '')
    ,nullif(CAST(blended_revenue as string), '')
    ,nullif(CAST(blended_payment_amount as string), '')
)) as id
,index_id as index_id
,MD5(concat(
     nullif(CAST(trip_month as string), '')
    ,nullif(CAST(ticket_type as string), '')
    ,nullif(CAST(travel_agency_number as string), '')
    ,nullif(CAST(country_of_sale as string), '')
    ,nullif(CAST(source as string), '')
    ,nullif(CAST(gds as string), '')
    ,nullif(CAST(ticketing_ai as string), '')
    ,nullif(CAST(poo_airport as string), '')
    ,nullif(CAST(distribution_channel as string), '')
    ,nullif(CAST(transaction as string), '')
    ,nullif(CAST(od_rbkd as string), '')
    ,nullif(CAST(dominant_marketing_airline as string), '')
    ,nullif(CAST(marketing_airline_1 as string), '')
    ,nullif(CAST(marketing_airline_2 as string), '')
    ,nullif(CAST(marketing_airline_3 as string), '')
    ,nullif(CAST(marketing_airline_4 as string), '')
    ,nullif(CAST(marketing_airline_5 as string), '')
    ,nullif(CAST(marketing_airline_6 as string), '')
    ,nullif(CAST(dominant_operating_airline as string), '')
    ,nullif(CAST(operating_airline_1 as string), '')
    ,nullif(CAST(operating_airline_2 as string), '')
    ,nullif(CAST(operating_airline_3 as string), '')
    ,nullif(CAST(operating_airline_4 as string), '')
    ,nullif(CAST(operating_airline_5 as string), '')
    ,nullif(CAST(operating_airline_6 as string), '')
    ,nullif(CAST(origin as string), '')
    ,nullif(CAST(stop_1 as string), '')
    ,nullif(CAST(stop_2 as string), '')
    ,nullif(CAST(stop_3 as string), '')
    ,nullif(CAST(stop_4 as string), '')
    ,nullif(CAST(stop_5 as string), '')
    ,nullif(CAST(stop_6 as string), '')
    ,nullif(CAST(destination as string), '')
    ,nullif(CAST(non_stop_and_connecting_indicator as string), '')
    ,nullif(CAST(od_days_sold_prior_to_travel as string), '')
    ,nullif(CAST(duration_minutes as string), '')
)) as uuid
,nullif(CAST(trip_month as string), '') as trip_month
,nullif(CAST(ticket_type as string), '') as ticket_type
,nullif(CAST(travel_agency_number as string), '') as travel_agency_number
,nullif(CAST(travel_agency_name as string), '') as travel_agency_name
,nullif(CAST(country_of_sale as string), '') as country_of_sale
,nullif(CAST(source as string), '') as source
,nullif(CAST(gds as string), '') as gds
,nullif(CAST(ticketing_ai as string), '') as ticketing_ai
,nullif(CAST(poo_airport as string), '') as poo_airport
,nullif(CAST(distribution_channel as string), '') as distribution_channel
,nullif(CAST(transaction as string), '') as transaction
,nullif(CAST(od_dominant_cabin_class as string), '') as od_dominant_cabin_class
,nullif(CAST(od_rbkd as string), '') as od_rbkd
,nullif(CAST(dominant_marketing_airline as string), '') as dominant_marketing_airline
,nullif(CAST(marketing_airline_1 as string), '') as marketing_airline_1
,nullif(CAST(marketing_airline_2 as string), '') as marketing_airline_2
,nullif(CAST(marketing_airline_3 as string), '') as marketing_airline_3
,nullif(CAST(marketing_airline_4 as string), '') as marketing_airline_4
,nullif(CAST(marketing_airline_5 as string), '') as marketing_airline_5
,nullif(CAST(marketing_airline_6 as string), '') as marketing_airline_6
,nullif(CAST(dominant_operating_airline as string), '') as dominant_operating_airline
,nullif(CAST(operating_airline_1 as string), '') as operating_airline_1
,nullif(CAST(operating_airline_2 as string), '') as operating_airline_2
,nullif(CAST(operating_airline_3 as string), '') as operating_airline_3
,nullif(CAST(operating_airline_4 as string), '') as operating_airline_4
,nullif(CAST(operating_airline_5 as string), '') as operating_airline_5
,nullif(CAST(operating_airline_6 as string), '') as operating_airline_6
,nullif(CAST(origin as string), '') as origin
,nullif(CAST(trip_origin_city as string), '') as trip_origin_city
,nullif(CAST(trip_origin_country_code as string), '') as trip_origin_country_code
,nullif(CAST(trip_origin_country_name as string), '') as trip_origin_country_name
,nullif(CAST(trip_origin_region as string), '') as trip_origin_region
,nullif(CAST(stop_1 as string), '') as stop_1
,nullif(CAST(stop_2 as string), '') as stop_2
,nullif(CAST(stop_3 as string), '') as stop_3
,nullif(CAST(stop_4 as string), '') as stop_4
,nullif(CAST(stop_5 as string), '') as stop_5
,nullif(CAST(stop_6 as string), '') as stop_6
,nullif(CAST(destination as string), '') as destination
,nullif(CAST(trip_destination_city as string), '') as trip_destination_city
,nullif(CAST(trip_destination_country_code as string), '') as trip_destination_country_code
,nullif(CAST(trip_destination_country_name as string), '') as trip_destination_country_name
,nullif(CAST(trip_destination_region as string), '') as trip_destination_region
,nullif(CAST(non_stop_and_connecting_indicator as string), '') as non_stop_and_connecting_indicator
,nullif(CAST(od_days_sold_prior_to_travel as string), '') as od_days_sold_prior_to_travel
,nullif(CAST(duration_minutes as string), '') as duration_minutes
,nullif(CAST(distance as string), '') as distance
,nullif(CAST(pax as string), '') as pax
,nullif(CAST(blended_average_fare as string), '') as blended_average_fare
,nullif(CAST(blended_revenue as string), '') as blended_revenue
,nullif(CAST(blended_payment_amount as string), '') as blended_payment_amount
,csv_file_name as csv_file_name
from df
        """
        
        # 在 parse_csv 方法中，执行 COPY 命令之前添加：
        # Path(dist_file_full_path).mkdir(parents=True, exist_ok=True)
        
        duckdb.sql(f"copy ({select_sql}) to '{dist_file_full_path}' (HEADER 'false', DELIMITER ',', NULLSTR '')")
        log.info(f'parse {dist_file_full_path} success')
        
        return processed_file_name, param_file
    
    
    def write2doris(self, file_name: str, doris_table_name, doris_table_columns):
        """
            1. 拼接 csv_file_name和base_doiwnload_path 为 full_file_name
            2. 使用doris_utils.py工具将数据写入到doris，doris_utils.py的具体使用方法，间文件内的main函数
        """
        # 1. 拼接文件路径
        full_file_name = os.path.join(self.download_path, file_name)
        nacos_config = get_config(settings.config_group, settings.secret_config, settings.nacos_base_url)
        stream_load(
            file = full_file_name
            ,host = nacos_config['doris'][0]['host']
            ,user = nacos_config['doris'][0]['user']
            ,password = nacos_config['doris'][0]['password']
            ,database = 'ods'
            ,table_name = doris_table_name
            ,columns = doris_table_columns
            ,format = 'csv'
            ,compress_type = ''
            ,column_separator = ','
            ,max_retries = 3  # 最多重试3次
            ,backoff_factor = 1.5  # 退避系数
        )