import json
import requests
from model.job import Job
from persistent.tokens_mapper import TokensMapper
from common.log_utils import log
from common.nacos_utils import get_config
from common.common_utils import settings
import time

class DDSService:
    
    # 默认值
    submit_url = "https://api.dds.iata.org/dds-reports/api/request-report"
    get_job_report_url = "https://api.dds.iata.org/system-management/management/systems/user-conveniences/report-histories"
    download_url = "https://api.dds.iata.org/user-conveniences/api/user-conveniences/download/report-histories"
    download_path = './downloads'
    N_TOKEN_FLASH_SECOND = 60 * 60 * 1
    
    max_connect_timeout_second = 30
    max_read_timeout_second = 2900
    
    def __init__(self, common_json: dict):
        nacos_config = get_config(
            settings.config_group, 
            settings.secret_config, 
            settings.nacos_base_url
        )

        dds_api_url = nacos_config['dds']['api_url']
        self.submit_url = f"{dds_api_url}/{common_json['submit_url']}"
        self.get_job_report_url = f"{dds_api_url}/{common_json['get_job_report_url']}"
        self.download_url = f"{dds_api_url}/{common_json['download_url']}"
        self.download_path = f"{common_json['project_base_path']}/{common_json['downloads_path']}"
        self.N_TOKEN_FLASH_SECOND = common_json['n_token_flash_second']
        
        if common_json['max_connect_timeout_second']:
            self.max_connect_timeout_second = common_json['max_connect_timeout_second']
            
        if common_json['max_read_timeout_second']:
            self.max_read_timeout_second = common_json['max_read_timeout_second']
    
    

    
    def submit(self, job: Job, saved_configuration):
        
        payload = json.dumps({
            "userId": "68215f49d624fd55d2fd3ee3",
            "reportNames": "ddsTicketingPivot",
            "savedConfiguration": f"{saved_configuration}",
            "mode": "runInBackground",
            "subscriptionType": "dds",
            "timeOut": 30000,
            "scheduledReport": {
                "savedConfigurationName": job.saved_configuration_name,
                "id": "6879e4b230418d03278e4d34"
            },
            "exportAs": None
        })

        # 从数据提取token
        result = TokensMapper().get_latest()
        
        if result is None or result.token is None:
            raise Exception("token is None")
        
        headers = {
            'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'Content-Type': "application/json",
            'sec-ch-ua': "\"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Microsoft Edge\";v=\"128\"",
            'sec-ch-ua-mobile': "?0",
            'authorization': f'{result.token}',
            'ngsw-bypass': "true",
            'sec-ch-ua-platform': "\"macOS\"",
            'origin': "https://dds.iata.org",
            'sec-fetch-site': "same-site",
            'sec-fetch-mode': "cors",
            'sec-fetch-dest': "empty",
            'referer': "https://dds.iata.org/",
            'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6,en-GB;q=0.5",
            'priority': "u=1, i"
        }

        response = requests.post(self.submit_url, data=payload, headers=headers, verify=False, timeout=(self.max_connect_timeout_second,self.max_read_timeout_second))
        if response.ok and response.status_code == 201:
            data = response.json()
            job.saved_configuration_name = data['savedConfigurationName']
            job.status = data['status']
            job.invocation_time = data['invocationTime']
            log.info(f'提交任务成功: {job}')
        else:
            log.error(f'提交任务失败，可能是token 失效, 内容为: {response.text}')
            return None
        
        return job
    
    def download_job(self, job):
        
        params = {
            'reportHistoryIds': f"{job.job_id}"
        }
        
        # 从数据提取token
        result = TokensMapper().get_latest()
        
        if result is None or result.token is None:
            raise Exception("token is None")
        
        headers = {
            'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'sec-ch-ua': "\"Not-A.Brand\";v=\"99\", \"Chromium\";v=\"124\"",
            'sec-ch-ua-mobile': "?0",
            'authorization': f'{result.token}',
            'sec-ch-ua-platform': "\"macOS\"",
            'origin': "https://dds.iata.org",
            'sec-fetch-site': "same-site",
            'sec-fetch-mode': "cors",
            'sec-fetch-dest': "empty",
            'referer': "https://dds.iata.org/",
            'accept-language': "zh-CN,zh;q=0.9",
            'priority': "u=1, i"
        }
        file_name = f'{job.csv_file_name}.csv'
        import time
        import shutil
        import os
        # 使用流式下载，避免内存问题，支持断点续传

        if not os.path.exists(self.download_path):
            os.makedirs(self.download_path, exist_ok=True)

        max_retries = 3
        temp_path = f"{self.download_path}/{file_name}.tmp"
        final_path = f"{self.download_path}/{file_name}"
        
        msbytes = 20 * 1024 
        for retry in range(max_retries):
            try:
                # 检查临时文件是否存在，获取已下载大小
                downloaded = 0
                if os.path.exists(temp_path):
                    downloaded = os.path.getsize(temp_path)
                    # log.info(f"发现临时文件，已下载: {downloaded / (1024 * 1024):.2f} MB，准备断点续传")
                    log.info(f"发现临时文件，已下载: {downloaded / (1024 * 1024):.2f} MB，准备断点续传失败")
                    os.remove(temp_path)
                    downloaded = 0
                    log.info(f"已删除临时文件{temp_path}")
                
                # 设置Range头进行断点续传
                resume_headers = headers.copy()
                if downloaded > 0:
                    resume_headers['Range'] = f'bytes={downloaded}-'
                
                with requests.get(self.download_url, params=params, headers=resume_headers, 
                                stream=True, verify=False, timeout=(self.max_connect_timeout_second,self.max_read_timeout_second)
                                ) as response:
                    
                    # 检查响应状态码
                    if response.status_code not in [200, 206]:  # 206是部分内容响应
                        log.error(f"请求失败，状态码: {response.status_code}")
                        if retry < max_retries - 1:
                            time.sleep(1)
                            continue
                        else:
                            return None
                    
                    # 获取文件总大小
                    if response.status_code == 206:
                        # 断点续传响应，从Content-Range获取总大小
                        content_range = response.headers.get('content-range', '')
                        if content_range:
                            file_size = int(content_range.split('/')[-1])
                        else:
                            file_size = downloaded + int(response.headers.get('content-length', 0))
                    else:
                        # 全新下载
                        file_size = int(response.headers.get('content-length', 0))
                        downloaded = 0  # 重置已下载大小
                    
                    if file_size > 0:
                        log.info(f"文件总大小: {file_size / (1024 * 1024):.2f} MB")
                        if downloaded > 0:
                            log.info(f"断点续传，剩余: {(file_size - downloaded) / (1024 * 1024):.2f} MB")
                    
                    chunk_size = 8192  # 8KB 的块大小
                    
                    # 打开文件进行写入（断点续传用追加模式）
                    file_mode = 'ab' if downloaded > 0 and response.status_code == 206 else 'wb'
                    
                    with open(temp_path, file_mode) as f:
                        start_time = time.time()
                        last_log_time = start_time
                        initial_downloaded = downloaded
                        
                        rate_limit_start_time = start_time
                        bytes_in_window = 0
                        
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:  # 过滤掉保持连接的空块
                                chunk_start_time = time.time()
                                f.write(chunk)
                                downloaded += len(chunk)
                                bytes_in_window += len(chunk)
                                
                                window_duration = chunk_start_time - rate_limit_start_time
                                
                                # 如果时间窗口超过1秒，重置计数
                                if window_duration >= 1.0:
                                    rate_limit_start_time = chunk_start_time
                                    bytes_in_window = len(chunk)
                                else:
                                    expected_max_bytes = msbytes * window_duration
                                    if bytes_in_window > expected_max_bytes:
                                        # 计算需要等待的时间
                                        excess_bytes = bytes_in_window - expected_max_bytes
                                        sleep_time = excess_bytes / msbytes
                                        if sleep_time > 0:
                                            time.sleep(sleep_time)
                                
                                # 每隔10秒记录下载进度
                                current_time = time.time()
                                if current_time - last_log_time > 10:
                                    if file_size > 0:
                                        percent = (downloaded / file_size) * 100
                                        log.info(f"下载进度: {percent:.1f}% ({downloaded / (1024 * 1024):.2f} MB / {file_size / (1024 * 1024):.2f} MB) ")
                                    else:
                                        log.info(f"已下载: {downloaded / (1024 * 1024):.2f} MB ")
                                    last_log_time = current_time
                        
                        # 验证文件完整性
                        if file_size > 0 and downloaded != file_size:
                            log.warning(f"文件大小不匹配，期望: {file_size}, 实际: {downloaded}")
                            if retry < max_retries - 1:
                                continue
                            else:
                                log.error("文件下载不完整，已达到最大重试次数")
                                return None
                    
                    # 下载完成后移动到最终位置
                    shutil.move(temp_path, final_path)
                    total_time = time.time() - start_time
                    avg_speed = (downloaded - initial_downloaded) / total_time / 1024 if total_time > 0 else 0
                    log.info(f"文件 {file_name} 下载成功，耗时: {total_time:.2f} 秒，平均速度: {avg_speed:.2f} KB/s")
                    
                    return file_name
                    
            except (requests.exceptions.RequestException, IOError) as e:
                log.error(f"下载失败 (尝试 {retry+1}/{max_retries}): {str(e)}")
                
                # 如果是网络错误且有部分文件，保留临时文件用于断点续传
                if isinstance(e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)):
                    if os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                        log.info("保留临时文件用于断点续传")
                    
                if retry < max_retries - 1:
                    wait_time = 2 * (retry + 1)
                    log.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 最后一次重试失败，清理临时文件
                    if os.path.exists(temp_path):
                        try:
                            os.remove(temp_path)
                            log.info("清理临时文件")
                        except:
                            pass
                    return None

        return None
    
    def login_invalid(self):
        """ 登录失效 """
        tokens_mapper = TokensMapper()
        # 兼容重复登录问题， 如果仅N_TOKEN_FLASH_SECOND内
        tokens = tokens_mapper.get_end_tokens(n_token_flash_second=self.N_TOKEN_FLASH_SECOND)
        if tokens is None:
            return True
        else:
            return False
    
    def get_job_status(self, job):
        
        # try中有可能抛异常，查看max_retry是否为0，如果不为0 就继续执行，否则就抛异常
        max_retry = 3
        retry_count = 0
        while retry_count < max_retry:
            try:
                params = {
                'type': "all",
                'pageSize': "50",
                'pageNumber': "1"
                }

                # 从数据提取token
                result = TokensMapper().get_latest()

                if result is None or result.token is None:
                    raise Exception("token is None")

                headers = {
                    'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                    'Accept': "application/json, text/plain, */*",
                    'Accept-Encoding': "gzip, deflate, br, zstd",
                    'sec-ch-ua': "\"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Microsoft Edge\";v=\"128\"",
                    'sec-ch-ua-mobile': "?0",
                    'authorization': f'{result.token}',
                    'sec-ch-ua-platform': "\"macOS\"",
                    'origin': "https://dds.iata.org",
                    'sec-fetch-site': "same-site",
                    'sec-fetch-mode': "cors",
                    'sec-fetch-dest': "empty",
                    'referer': "https://dds.iata.org/",
                    'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6,en-GB;q=0.5",
                    'priority': "u=1, i"
                }

                response = requests.get(self.get_job_report_url, params=params, headers=headers, verify=False, timeout=(self.max_connect_timeout_second,self.max_read_timeout_second))

                # log.debug(f'获取到任务为: {response.text}')

                data = response.json()
                # for item in data['data']:
                #     log.debug(f'获取到任务为: {item["id"]} : {item["savedConfigurationName"]}')

                if response.ok and response.status_code == 200:
                    # 将json解析转换成list[Job]
                    for item in data['data']:
                        if item['savedConfigurationName'] == job.saved_configuration_name:
                            job.job_id = item['id']
                            job.status = item['status']
                            return job
                else:
                    log.error(f'获取任务失败，相应内容为: {data}')
                    return None
               
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retry:
                    log.error(f'获取任务状态失败，已达到最大重试次数({max_retry})，异常为: {str(e)}')
                    raise e
                log.warning(f'获取任务状态失败，正在进行第{retry_count}次重试，异常为: {str(e)}')
                time.sleep(60 * retry_count)  # 指数退避策略
    
    

if __name__ == "__main__":
    str = '2025-07-18T03:28:48.381395757Z'
    # print(parse(str))
