import time 
import tempfile
import platform

from selenium.common.exceptions import TimeoutException
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium import webdriver

from persistent.tokens_mapper import TokensMapper
from model.tokens import Tokens

from abc import ABC, abstractmethod

from common.common_utils import settings
from common.nacos_utils import get_config

from common.log_utils import log

import random

class ELHandler(ABC):
    _next = None 
    STEP2STEP_INTERVAL = 1
    def set_next(self, handler):
        self._next = handler
        return handler
    
    @abstractmethod
    def handle(self, request):
        if self._next:
            return self._next.handle(request)
        return None


class ButtonHandler(ELHandler):
    def handle(self, request, wait_driver):
        if request['type'] == 'button':
            element = wait_driver.until(EC.presence_of_element_located((By.XPATH, request['xpath'])))
            time.sleep(self.STEP2STEP_INTERVAL)
            element.click()
            return f"button clicked {request['xpath']}"
        
        return super().handle(request)


class InputHandler(ELHandler):
    def handle(self, request, wait_driver):
        if request['type'] == 'input':
            element = wait_driver.until(EC.presence_of_element_located((By.XPATH, request['xpath'])))
            time.sleep(self.STEP2STEP_INTERVAL)
            element.clear()
            time.sleep(self.STEP2STEP_INTERVAL)
            element.send_keys(request['value'])
            return f"input send text {request['value']}"
        
        return super().handle(request)
       
def get_handler_chain():
    input_handler = InputHandler()
    button_handler = ButtonHandler()
    input_handler.set_next(button_handler)
    return input_handler

class WebService():
    
    # 最大超时时间
    MAX_TIMEOUT_SECIBLE: int = 60
    # 每一步操作之间定义一个等待间隔
    STEP2STEP_INTERVAL: int = 1
    # 最大运行内存
    RUNTIME_MEM: int = 4096

    common_json = None
    
    # DDS登录网站 'http://dds.iata.org'
    DDS_URL: str = None
    EMAIL_KEY: str = None
    PWD_KEY: str = None
    
    # 执行操作步骤
    login_sttr_loc : dict = None
    
    def __init_login_step__(self):
        # 定位页面相关 XPATH
        self.login_sttr_loc: dict = {
            'step1': {
                'email_input': {
                    'type': 'input',
                    'xpath': '//*[@id="signInName"]',
                    'value': self.EMAIL_KEY
                },
                'validate_email_btn': {
                    'type': 'button',
                    'xpath': '//*[@id="continue"]'
                }
            },'step2': {
                'pwd_input': {
                    'type': 'input',
                    'xpath': '/html/body/div[4]/div/div/div[2]/div/div/c-portal-login/c-portal-login-container/div/div/div[1]/div[2]/slot/span/div/div/c-portal-login-card-container/div/div/slot[3]/span/div/div/div[4]/div[1]/input',
                    'value': self.PWD_KEY
                },
                'login_btn': {
                    'type': 'button',
                    'xpath': '/html/body/div[4]/div/div/div[2]/div/div/c-portal-login/c-portal-login-container/div/div/div[1]/div[2]/slot/span/div/div/c-portal-login-card-container/div/div/slot[3]/span/div/div/button'
                }
            }, 'step3': {
                'home_dds_btn': {
                    'type': 'button',
                    'xpath': '//*[@id="mat-dialog-0"]/iata-subscription-info/div/div/div/iata-button/button'
                }
            }
        }

    def __init__(self, common_json:dict):
        # 初始化参数
        nacos_config = get_config(
            settings.config_group, 
            settings.secret_config, 
            settings.nacos_base_url
        )
        
        self.common_json = common_json
        dds_config = nacos_config['dds']
        self.DDS_URL = dds_config['url']
        self.EMAIL_KEY = dds_config['username']
        self.PWD_KEY = dds_config['password']
        
        self.RUNTIME_MEM = self.common_json['chrome_runtime_mem']
        self.MAX_TIMEOUT_SECIBLE = self.common_json['selenium_operate_max_timeout_second']
        self.STEP2STEP_INTERVAL = self.common_json['selenium_step2step_interval_second']
        remote_debugging_port = self.common_json['remote_debugging_port']
        
        # 初始化登录步骤
        self.__init_login_step__()
        
        # 1.创建chrome driver
        # 设置Chrome选项
        options = Options()
        options.add_argument("--headless")  # 无头模式
        options.add_argument("--window-size=1920,1080")  # 设置窗口大小
        options.add_argument("--disable-gpu")  # 禁用GPU加速
        options.add_argument("--no-sandbox")  # Linux必需选项
        options.add_argument("--disable-dev-shm-usage")  # Linux必需选项
        options.add_argument("--disable-web-security")  # 禁用Web安全
        options.add_argument("--disable-features=VizDisplayCompositor")  # 禁用某些功能
        options.add_argument("--disable-extensions")  # 禁用扩展
        options.add_argument("--disable-plugins")  # 禁用插件
        options.add_argument("--disable-background-timer-throttling") # 禁用后台标签页的定时器节流
        options.add_argument("--disable-renderer-backgrounding") # 防止浏览器将渲染进程（Renderer）优先级降低
        options.add_argument("--disable-backgrounding-occluded-windows") # 禁用被遮挡窗口的后台优化
        options.add_argument("--disable-ipc-flooding-protection")  # 禁用IPC洪水保护
        options.add_argument("--disable-hang-monitor")  # 禁用挂起监视器
        options.add_argument("--disable-client-side-phishing-detection")  # 禁用客户端网络钓鱼检测
        options.add_argument("--disable-popup-blocking")  # 禁用弹出窗口阻止
        options.add_argument("--disable-prompt-on-repost")  # 禁用重新发布提示
        options.add_argument("--disable-sync")  # 禁用同步
        options.add_argument("--disable-translate")  # 禁用翻译
        options.add_argument(f"--max_old_space_size={self.RUNTIME_MEM}")  # 增加内存限制
        options.add_argument(f"--remote-debugging-port={remote_debugging_port}")  # 添加远程调试端口
        options.add_argument("--disable-software-rasterizer")  # 禁用软件光栅化
        options.add_argument("--disable-background-networking")  # 禁用后台网络
        options.add_argument("--disable-default-apps")  # 禁用默认应用
        options.add_argument("--disable-features=TranslateUI")  # 禁用翻译UI
        options.add_argument("--disable-features=VizDisplayCompositor")  # 禁用VizDisplayCompositor
        options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.100 Safari/537.36")

        # 解决用户数据目录问题的方法
        temp_dir = tempfile.mkdtemp()
        options.add_argument(f"--user-data-dir={temp_dir}")  # 使用临时目录
        options.add_argument("--profile-directory=Default")  # 指定配置文件目录

        # 根据平台设置服务选项
        if platform.system() == "Windows":
            # 应对未来不确定因素
            service = Service()
            service.creation_flags = 0x08000000  # CREATE_NO_WINDOW flag on Windows
            self.driver = webdriver.Chrome(service=service, options=options)
        elif platform.system() == "Darwin": 
            # 适配开发环境（自己开发环境是mac）
            self.driver = webdriver.ChromiumEdge()
        else:
            # 适配生产环境Linux
            service = Service()
            self.driver = webdriver.Chrome(service=service, options=options)

        self.wait = WebDriverWait(self.driver, self.MAX_TIMEOUT_SECIBLE)

    def run(self, operate_chain: dict):
        for step in operate_chain:
            log.info(f'执行step: {step}：操作内容为: {operate_chain[step]}')
            self.handler_chain(operate_chain[step])

    def login(self):
        # 失败重试次数
        login_max_retry_count = self.common_json['login_max_retry_count']
        # 重新启动间隔
        login_retry_interval_second = self.common_json['login_retry_interval_second']
        
        retry_count = 0
        last_exception = None
        while retry_count < login_max_retry_count:
            try:
                
                # 1. 打开目标页面
                self.driver.get(self.DDS_URL)

                # 2. 执行登录步骤
                self.run(self.login_sttr_loc)
                
                # 获取Token
                storage_tokens = self.driver.execute_script(self.get_session_token())
                token = f'Bearer {storage_tokens}'
                tokens_mapper = TokensMapper()
                token = tokens_mapper.add(Tokens(token=token))
                log.debug(token)
                
                # 4. 将历史Token逻辑删除, 1小时以前的Token
                tokens_mapper.update_history_token(token.insert_time)
                
                return token
                
            except TimeoutException as e:
                last_exception = e
                log.error(f"第{retry_count + 1}次尝试: 页面加载超时")
            except Exception as e:
                last_exception = e
                log.error(f"第{retry_count + 1}次尝试: 发生错误: {e}")
            finally:
                self.__close__()
            
            retry_count += 1
            if retry_count < login_max_retry_count:
                log.info(f"等待{login_retry_interval_second}秒后进行第{retry_count + 1}次重试...")
                time.sleep(login_retry_interval_second)
                # 重新初始化driver
                self.__init__(self.common_json)
        
        
        if token is None:
            log.error(f"登录失败,已重试{login_max_retry_count}次,最后一次错误: {last_exception}")    
            exit(-1)
            
        return None

    def __close__(self):
        self.driver.quit()

    def handler_chain(self, operate_chain: dict):
        if operate_chain:
            for k in operate_chain:
                operate = operate_chain[k]
                # 取出对应的操作类型
                operate_type = operate_chain[k]['type']
                
                if operate_type == 'button':
                    self._handle_button(operate)
                elif operate_type == 'input':
                    self._handle_input(operate)
                elif operate_type == 'select':
                    self._handle_select(operate)
                elif operate_type == 'checkbox':
                    self._handle_checkbox(operate)
                elif operate_type == 'radio':
                    self._handle_radio(operate)
                elif operate_type == 'textarea':
                    self._handle_textarea(operate)
                elif operate_type == 'file':
                    self._handle_file(operate)
                elif operate_type == 'link':
                    self._handle_link(operate)
                else:
                    raise Exception(f'Operation "{operate_type}" is not supported')
    def _handle_button(self, operate: dict):
        # 处理按钮操作
        log.debug(f"Handling button with params: {operate}")
        element = self.wait.until(EC.presence_of_element_located((By.XPATH,operate['xpath'])))
        time.sleep(self.STEP2STEP_INTERVAL)
        element.click()

    def _handle_input(self, operate: dict):
        # 处理输入框操作
        log.debug(f"Handling input with params: {operate}")
        element = self.wait.until(EC.presence_of_element_located((By.XPATH,operate['xpath'])))
        time.sleep(self.STEP2STEP_INTERVAL)
        element.clear()
        time.sleep(self.STEP2STEP_INTERVAL)
        element.send_keys(operate['value'])

    def _handle_select(self, operate: dict):
        # 处理下拉选择框操作
        log.debug(f"Handling select with params: {operate}")

    def _handle_checkbox(self, operate: dict):
        # 处理复选框操作
        log.debug(f"Handling checkbox with params: {operate}")

    def _handle_radio(self, operate: dict):
        # 处理单选按钮操作
        log.debug(f"Handling radio with params: {operate}")

    def _handle_textarea(self, operate: dict):
        # 处理文本区域操作
        log.debug(f"Handling textarea with params: {operate}")

    def _handle_file(self, operate: dict):
        # 处理文件上传操作
        log.debug(f"Handling file with params: {operate}")

    def _handle_link(self, operate: dict):
        # 处理链接操作
        log.debug(f"Handling link with params: {operate}")

    def get_session_token(self):
        """提供获取session_token的函数"""
        js_script = """
            var bearerTokens = {};
            try {
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    var value = sessionStorage.getItem(key);
                    try {
                        var data = JSON.parse(value);
                        if (data && data.tokenType === 'Bearer' && data.secret) {
                            bearerTokens['secret'] = data.secret;
                        }
                    } catch(e) {
                        // 跳过
                    }
                }
            } catch(e) {
                console.log('sessionStorage access failed:', e);
            }
            return bearerTokens['secret'];
        """
        return js_script
    

if __name__ == '__main__':
    common_json = {
        "max_polling_time_out":3600
        ,"loop_interval":5
        ,"job_success" : "success"
        ,"login_max_retry_count": 3
        ,"login_retry_interval_second": 5
        ,"chrome_runtime_mem": 4096
        ,"selenium_operate_max_timeout_second": 60
        ,"selenium_step2step_interval_second": 1
        ,"remote_debugging_port" : 9222
        ,"max_connect_timeout_second": 30
        ,"max_read_timeout_second": 2900
    }
    WebService(common_json).login()
