from service.parse_service import ParseService

common_json = {
        "max_polling_time_out":3600
        ,"submit_url":"dds-reports/api/request-report"
        ,"get_job_report_url":"system-management/management/systems/user-conveniences/report-histories"
        ,"download_url":"user-conveniences/api/user-conveniences/download/report-histories"
        ,"project_base_path": "/opt/app/dds"
        ,"downloads_path": "downloads"
        ,"n_token_flash_second": 3600
        ,"loop_interval":5
        ,"job_success" : "success"
        ,"login_max_retry_count": 3
        ,"login_retry_interval_second": 5
        ,"chrome_runtime_mem": 4096
        ,"selenium_operate_max_timeout_second": 60
        ,"selenium_step2step_interval_second": 1
        ,"remote_debugging_port" : 9222
        ,"max_connect_timeout_second": 30
        ,"max_read_timeout_second": 2900
        ,"doris_table_name": "ods_mkt_dds_sales_monitoring"
        ,"doris_table_columns": "id,index_id,uuid,trip_month,ticket_type,travel_agency_number,travel_agency_name,country_of_sale,source,gds,ticketing_ai,poo_airport,distribution_channel,transaction,od_dominant_cabin_class,od_rbkd,dominant_marketing_airline,marketing_airline_1,marketing_airline_2,marketing_airline_3,marketing_airline_4,marketing_airline_5,marketing_airline_6,dominant_operating_airline,operating_airline_1,operating_airline_2,operating_airline_3,operating_airline_4,operating_airline_5,operating_airline_6,origin,trip_origin_city,trip_origin_country_code,trip_origin_country_name,trip_origin_region,stop_1,stop_2,stop_3,stop_4,stop_5,stop_6,destination,trip_destination_city,trip_destination_country_code,trip_destination_country_name,trip_destination_region,non_stop_and_connecting_indicator,od_days_sold_prior_to_travel,duration_minutes,distance,pax,blended_average_fare,blended_revenue,blended_payment_amount,csv_file_name"
    }

def test_parse_csv():
    """ 写一个parse 的测试函数,
    csv_file_name 27_20250115.csv
    saved_configuration = read_json('./config/report_sample_27.json')
    """

    csv_file_name = '/Users/<USER>/Downloads/37_20250115.csv'
    
    #with open('./config/report_sample_27.json', 'r', encoding='utf-8') as file:
    saved_configuration = '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'
    
    saved_configuration = str(saved_configuration).replace("\\n", "").replace(" ", "").replace("'","")
    
    parse_service = ParseService(common_json)
    parse_service.parse_csv(csv_file_name,saved_configuration)


def test_write2doris():
    parse_service = ParseService(common_json)
    parse_service.write2doris('/Users/<USER>/Downloads/37_20250115p.csv')


test_write2doris()