import pandas as pd
import io
import math


def next_power_of_2(n):
    """
    找到大于等于给定数字n的最小的2的次方数
    
    参数:
        n (int): 输入的数字
    
    返回:
        int: 大于等于n的最小的2的次方数
    """
    if n <= 0:
        return 1
    
    # 如果n本身就是2的次方，直接返回n
    if n & (n - 1) == 0:
        return n
    
    # 计算log2(n)并向上取整，得到指数
    power = math.ceil(math.log2(n))
    
    # 返回2的power次方
    return 2 ** power

# 读取原始文件内容
with open('./downloads/27_20250115p.csv', 'r', encoding='utf-8') as file:
    lines = file.readlines()
print(f"原始文件总行数: {len(lines)}")
# 去除前13行和最后一行
clean_lines = lines[13:-1]  # 跳过前13行，去除最后一行
# 将清理后的数据转换为字符串
clean_data = ''.join(clean_lines)
# 使用StringIO创建一个类似文件的对象
data_io = io.StringIO(clean_data)
df = pd.read_csv(data_io)
print(df)
# 求每一列 max(字符串长度)

max_len = df.astype(str).apply(lambda x: next_power_of_2(x.str.len().max() * 3))
print(max_len)



