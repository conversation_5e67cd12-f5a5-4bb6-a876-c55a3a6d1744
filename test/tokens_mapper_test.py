
from persistent.tokens_mapper import TokensMapper
from model.tokens import Tokens


# 使用方式：
tokens_mapper = TokensMapper()
token = 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ik1tRV95d2dOa1hpa0phZlF2emFjYThWRENPbjZQbXlINThxN09lOEVreDgiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wShH3-93LTS9MxoBN85Dl8nXP51QhIJKiRCZeEw4M21GRgSa7iQBYgOn0KCEfSANXCImxjcIZ7nvGeikIrUnd0Sxyj-NYOtWN-O9Jbihy7hveC0wX1cq5EVUWk2jDski-qUrxUK05VMY4SsUN8G-Ba9sNqR9DdUcs4G_mGNgu0d4WGgSpYIEaSLVJwJ_vLJQAZoI7ej1L48R8lvbOnb5rctlLE7_rQYH4gIQLpq-pxpXbR3b_1XA7jR1PrUl08nKqIetQtY0hbDM3exGtqO3gY_wPW0ddvaY8ij3E52oTd7dnaE9V3J0m8KO8t9BPEINqzQ7QiZM74H8GowUgfYpdg'
def test_add(token: str):
    t1 = Tokens()
    t1.token = token
    tokens_mapper.add(t1)
    
# test_add(token)
    
# lists = [Tokens(token='123'),Tokens(token='123'),Tokens(token='123'),Tokens(token='123')]
def test_add_batch(lists : list[Tokens]):
    tokens_mapper.add_batch(lists)

# t1 = Tokens()
# t1.token = token
# t2 = Tokens()
# t2.token = token
# test_add_batch([t1, t2])

def test_get_by_id():
    id = 2
    token = tokens_mapper.get_by_id(id)
    print(token)

# test_get_by_id()

def test_get_all():
    lists = tokens_mapper.get_all()
    print(lists)
    
# test_get_all()
    

def test_get_latest():
    token = tokens_mapper.get_latest()
    print(token)
    
# test_get_latest()

def test_update_by_id():
    token = tokens_mapper.update_by_id(2, Tokens(token='433'))
    print(token)
    
# test_update_by_id()

def test_delete_by_id():
    id = 2
    result = tokens_mapper.delete_by_id(id)
    print(result)
    
# test_delete_by_id()

def test_count():
    num = tokens_mapper.count()
    print(num)
    
# test_count()

def test_update_history_token():
    token = tokens_mapper.get_by_id(3)
    print(token.insert_time)
    
    tokens_mapper.update_history_token(token.insert_time)

# test_update_history_token()

def test_get_end_tokens():
    s = 60 * 60 * 1
    tokens = tokens_mapper.get_end_tokens(s)
    print(tokens)

test_get_end_tokens()
