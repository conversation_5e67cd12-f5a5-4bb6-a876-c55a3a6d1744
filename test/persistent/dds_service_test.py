from service.dds_service import DDSService
from model.job import Job
from datetime import datetime

from common.config_utils import get_config_by_file
common_json = {
        "max_polling_time_out":3600
        ,"submit_url":"dds-reports/api/request-report"
        ,"get_job_report_url":"system-management/management/systems/user-conveniences/report-histories"
        ,"download_url":"user-conveniences/api/user-conveniences/download/report-histories"
        ,"project_base_path": "/opt/app/dds"
        ,"downloads_path": "downloads"
        ,"n_token_flash_second": 3600
        ,"loop_interval":5
        ,"job_success" : "success"
        ,"login_max_retry_count": 3
        ,"login_retry_interval_second": 5
        ,"chrome_runtime_mem": 4096
        ,"selenium_operate_max_timeout_second": 60
        ,"selenium_step2step_interval_second": 1
        ,"remote_debugging_port" : 9222
        ,"max_connect_timeout_second": 30
        ,"max_read_timeout_second": 2900
        ,"doris_table_name": "ods_mkt_dds_sales_monitoring"
        ,"doris_table_columns": "id,index_id,uuid,trip_month,ticket_type,travel_agency_number,travel_agency_name,country_of_sale,source,gds,ticketing_ai,poo_airport,distribution_channel,transaction,od_dominant_cabin_class,od_rbkd,dominant_marketing_airline,marketing_airline_1,marketing_airline_2,marketing_airline_3,marketing_airline_4,marketing_airline_5,marketing_airline_6,dominant_operating_airline,operating_airline_1,operating_airline_2,operating_airline_3,operating_airline_4,operating_airline_5,operating_airline_6,origin,trip_origin_city,trip_origin_country_code,trip_origin_country_name,trip_origin_region,stop_1,stop_2,stop_3,stop_4,stop_5,stop_6,destination,trip_destination_city,trip_destination_country_code,trip_destination_country_name,trip_destination_region,non_stop_and_connecting_indicator,od_days_sold_prior_to_travel,duration_minutes,distance,pax,blended_average_fare,blended_revenue,blended_payment_amount,csv_file_name"
        ,"doris_table_name_param": "ods_mkt_dds_sales_monitoring_param"
        ,"doris_table_columns_param": "id,csv_file_name,saved_configuration"
    }


def test_submit():
    s_year, s_month, s_day = 2025, 1, 13
    e_year, e_month, e_day = 2025, 1, 15
    config_name = 'config/report_sample_1.json'
    cjson = get_config_by_file(s_year, s_month, s_day, e_year, e_month, e_day, config_name)
   
    service = DDSService(common_json)
    job = Job()
    job.saved_configuration = cjson
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S%f")[:-3]
    download_date = datetime(e_year, e_month, e_day).strftime("%Y%m%d")
    job.saved_configuration_name = f'27_{timestamp}'
    job.csv_file_name = f'27_{download_date}'
    job = service.submit(job)
    print(job.to_dict())
    
def test_get_job_status():
    service = DDSService(common_json)
    job = Job()
    job.saved_configuration_name = '24_20250801_154921569'
    job = service.get_job_status(job)
    print(f" ========{job.status}===={job.id}=={job.job_id}== ")
    
def test_download_job():
    # download_keys = {"687e03bd30418d03278e5986" : "spneed-cn-D-0628"
    #     ,"687e035430418d03278e597c" : "spneed-cn-D-0627"
    #     ,"687e031a30418d03278e5977" : "spneed-cn-D-0626"
    #     ,"687e02da30418d03278e5975" : "spneed-cn-D-0625"
    #     ,"687e029c30418d03278e596d" : "spneed-cn-D-0624"
    #     ,"687e026130418d03278e5968" : "spneed-cn-D-0623"
    #     ,"687e021d30418d03278e5961" : "spneed-cn-D-0622"
    #     ,"687e01da30418d03278e595d" : "spneed-cn-D-0621"
    #     ,"687e019a0406b04c352d0e40" : "spneed-cn-D-0620"
    #     ,"687e013830418d03278e5948" : "spneed-cn-D-0619"
    #     ,"687e00eb30418d03278e5942" : "spneed-cn-D-0618"
    #     ,"687e00aa30418d03278e593e" : "spneed-cn-D-0617"
    #     ,"687e00650406b04c352d0e34" : "spneed-cn-D-0616"
    #     ,"687df9060406b04c352d0df6" : "spneed-cn-D-0615_1"
    #     ,"687df7cd30418d03278e58ba" : "spneed-cn-D-0615_2"}
    
    
    download_keys =  {"687efd6f30418d03278e5e8f" : "spneed-cn-I-0628"
    # ,"687efb9e30418d03278e5e7b" : "spneed-cn-I-0627"
    # ,"687efb6530418d03278e5e7a" : "spneed-cn-I-0626"
    # ,"687efb2c30418d03278e5e78" : "spneed-cn-I-0625"
    # ,"687efaf230418d03278e5e76" : "spneed-cn-I-0624"
    # ,"687efab830418d03278e5e71" : "spneed-cn-I-0623"
    # ,"687efa7630418d03278e5e6e" : "spneed-cn-I-0622"
    # ,"687ef9290406b04c352d12c8" : "spneed-cn-I-0621"
    # ,"687ef8710406b04c352d12b5" : "spneed-cn-I-0620"
    # ,"687ef76d0406b04c352d12ab" : "spneed-cn-I-0619"
    # ,"687ef73930418d03278e5e57" : "spneed-cn-I-0618"
    # ,"687ef6ff0406b04c352d12a5" : "spneed-cn-I-0617"
    # ,"687ef6c130418d03278e5e4c" : "spneed-cn-I-0616"
    # ,"687ee5210406b04c352d124a" : "spneed-cn-I-0615"
    }
    common_json= {
        "max_polling_time_out":3600
        ,"submit_url":"dds-reports/api/request-report"
        ,"get_job_report_url":"system-management/management/systems/user-conveniences/report-histories"
        ,"download_url":"user-conveniences/api/user-conveniences/download/report-histories"
        ,"project_base_path": "/Users/<USER>/workspace/vscode/dds"
        ,"downloads_path": "downloads"
        ,"n_token_flash_second": 3600
        ,"loop_interval":5
        ,"job_success" : "success"
        ,"login_max_retry_count": 3
        ,"login_retry_interval_second": 5
        ,"chrome_runtime_mem": 4096
        ,"selenium_operate_max_timeout_second": 60
        ,"selenium_step2step_interval_second": 1
        ,"remote_debugging_port" : 9222
        ,"max_connect_timeout_second": 30
        ,"max_read_timeout_second": 2900
        ,"doris_table_name": "ods_mkt_dds_sales_monitoring"
        ,"doris_table_columns": "id,index_id,uuid,trip_month,ticket_type,travel_agency_number,travel_agency_name,country_of_sale,source,gds,ticketing_ai,poo_airport,distribution_channel,transaction,od_dominant_cabin_class,od_rbkd,dominant_marketing_airline,marketing_airline_1,marketing_airline_2,marketing_airline_3,marketing_airline_4,marketing_airline_5,marketing_airline_6,dominant_operating_airline,operating_airline_1,operating_airline_2,operating_airline_3,operating_airline_4,operating_airline_5,operating_airline_6,origin,trip_origin_city,trip_origin_country_code,trip_origin_country_name,trip_origin_region,stop_1,stop_2,stop_3,stop_4,stop_5,stop_6,destination,trip_destination_city,trip_destination_country_code,trip_destination_country_name,trip_destination_region,non_stop_and_connecting_indicator,od_days_sold_prior_to_travel,duration_minutes,distance,pax,blended_average_fare,blended_revenue,blended_payment_amount,csv_file_name"
    }
    
    service = DDSService(common_json)
    
    for key, value in download_keys.items():
        j = Job()
        j.job_id = key
        j.csv_file_name = value
        job = service.download_job(j)
        print(job)
        
    
        
    
def read_file(file_path):
    with open(file_path, 'r') as f:
        return f.read()
    
if __name__ == '__main__':
    
    test_get_job_status()
    