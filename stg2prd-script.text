# 1. 整体思路

![image.png](https://pic.yihao.de/pic/2025/08/07/68941aabd6ea2.png)

# 2. 编写stg-hdfs 到 prd-hdfs 的python 代码

```python
# -*- coding: utf-8 -*-
# @Time    : 2024/1/18 3:35 下午
# <AUTHOR> Hoey
from datetime import datetime
import time
import re
import os
from hdfs.client import InsecureClient
from common.common_utils import *


report_enum = {"od": "od_report", "bsr": "bsr_report", "cr": "cr_report"}

from_host = 't-d-datastorage-srv02'
from_port = '9870'
from_user_name = 'hdfs'

to_host = '**********'
to_port = '9870'
to_user_name = 'hdfs'

def get_hdfs_client(host,port,user_name):

    hdfs_url = f'http://{host}:{port}'
    try:
        client = InsecureClient(hdfs_url, user=user_name)
        # 简单测试连接是否有效
        client.list('/')
        return client
    except Exception as e:
        log(__name__).error(f"连接HDFS异常: {e}")
        time.sleep(0.1)
    
    raise ConnectionError("无法连接到任何HDFS节点")

def upload(local_path, hdfs_path, overwrite=False, max_retries=3):
    """
    递归上传本地文件或目录到HDFS
    
    :param local_path: 本地路径(文件或目录)
    :param hdfs_path: HDFS目标路径
    :param overwrite: 是否覆盖已存在的HDFS文件
    :param max_retries: 最大重试次数
    :return: (成功上传的文件数, 失败的文件数)
    """
    success_count = 0
    failure_count = 0
    
    try:
        client = get_hdfs_client(to_host,to_port,to_user_name)
        hdfs_base_path = hdfs_path
        
        # 检查本地路径是否存在
        if not os.path.exists(local_path):
            log(__name__).error(f"本地路径不存在: {local_path}")
            return (0, 1)
            
        # 如果是文件，直接上传
        if os.path.isfile(local_path):
            return _upload_single_file(client, local_path, hdfs_base_path, overwrite, max_retries)
            
        # 如果是目录，递归处理
        for root, dirs, files in os.walk(local_path):
            # 计算相对路径
            relative_path = os.path.relpath(root, local_path)
            hdfs_dir = os.path.join(hdfs_base_path, relative_path)
            
            # 创建HDFS目录
            if not client.status(hdfs_dir, strict=False):
                client.makedirs(hdfs_dir)
            
            # 上传文件
            for filename in files:
                local_file = os.path.join(root, filename)
                hdfs_file = os.path.join(hdfs_dir, filename)
                
                result = _upload_single_file(client, local_file, hdfs_file, overwrite, max_retries)
                success_count += result[0]
                failure_count += result[1]
                
        log(__name__).info(f"递归上传完成: 成功 {success_count} 个, 失败 {failure_count} 个")
        return (success_count, failure_count)
        
    except Exception as err:
        err_mes = re.sub('[\n\r].*', '', str(err))
        alert_info = f'递归上传失败: {local_path}\n错误信息: {err_mes}'
        log(__name__).error(alert_info)
        return (success_count, failure_count + 1)

def _upload_single_file(client, local_file, hdfs_file, overwrite, max_retries):
    """
    上传单个文件，带有重试机制
    
    :return: (成功为1,失败为0), (成功为0,失败为1)
    """
    for attempt in range(max_retries):
        try:
            # 检查是否需要覆盖
            if client.status(hdfs_file, strict=False) and not overwrite:
                log(__name__).warning(f"跳过已存在文件: {hdfs_file}")
                return (1, 0)
                
            client.upload(os.path.dirname(hdfs_file), local_file, overwrite=True)
            log(__name__).debug(f"上传成功: {local_file} -> {hdfs_file}")
            return (1, 0)
        except Exception as e:
            if attempt == max_retries - 1:
                err_mes = re.sub('[\n\r].*', '', str(e))
                log(__name__).error(f"上传失败(尝试 {attempt + 1} 次): {local_file}. 错误: {err_mes}")
                return (0, 1)
            time.sleep(1 * (attempt + 1))  # 指数退避

def download(hdfs_path, local_path, overwrite=False, max_retries=3):
    """
    递归下载HDFS文件或目录到本地
    
    :param hdfs_path: HDFS上的路径(文件或目录)
    :param local_path: 本地目标路径
    :param overwrite: 是否覆盖已存在的本地文件
    :param max_retries: 最大重试次数
    :return: (成功下载的文件数, 失败的文件数)
    """
    success_count = 0
    failure_count = 0
    
    try:
        client = get_hdfs_client(from_host,from_port,from_user_name)
        
        # 检查HDFS路径是否存在
        if not client.status(hdfs_path, strict=False):
            log(__name__).error(f"HDFS路径不存在: {hdfs_path}")
            return (0, 1)
            
        # 确保本地目录存在
        os.makedirs(local_path, exist_ok=True)
        
        # 如果是文件，直接下载
        if not client.status(hdfs_path)['type'] == 'DIRECTORY':
            return _download_single_file(client, hdfs_path, local_path, overwrite, max_retries)
            
        # 如果是目录，递归处理
        for root, dirs, files in client.walk(hdfs_path):
            # 计算相对路径
            relative_path = os.path.relpath(root, hdfs_path)
            local_dir = os.path.join(local_path, relative_path)
            
            # 创建本地目录
            os.makedirs(local_dir, exist_ok=True)
            
            # 下载文件
            for filename in files:
                hdfs_file = os.path.join(root, filename)
                local_file = os.path.join(local_dir, filename)
                
                result = _download_single_file(client, hdfs_file, local_file, overwrite, max_retries)
                success_count += result[0]
                failure_count += result[1]
                
        log(__name__).info(f"递归下载完成: 成功 {success_count} 个, 失败 {failure_count} 个")
        return (success_count, failure_count)
        
    except Exception as err:
        err_mes = re.sub('[\n\r].*', '', str(err))
        alert_info = f'递归下载失败: {hdfs_path}\n错误信息: {err_mes}'
        log(__name__).error(alert_info)
        return (success_count, failure_count + 1)

def _download_single_file(client, hdfs_file, local_file, overwrite, max_retries):
    """
    下载单个文件，带有重试机制
    
    :return: (成功为1,失败为0), (成功为0,失败为1)
    """
    # 检查本地文件是否已存在
    if os.path.exists(local_file) and not overwrite:
        log(__name__).warning(f"跳过已存在文件: {local_file}")
        return (1, 0)
        
    for attempt in range(max_retries):
        try:
            client.download(hdfs_file, local_file, overwrite=True)
            log(__name__).debug(f"下载成功: {hdfs_file} -> {local_file}")
            return (1, 0)
        except Exception as e:
            if attempt == max_retries - 1:
                err_mes = re.sub('[\n\r].*', '', str(e))
                log(__name__).error(f"下载失败(尝试 {attempt + 1} 次): {hdfs_file}. 错误: {err_mes}")
                return (0, 1)
            time.sleep(1 * (attempt + 1))  # 指数退避
    
    
if __name__ == '__main__':
    hdfs_path, local_path = '/user/hive/warehouse/dwd.db/emp_staff_record_d', '/Users/<USER>/Downloads/tmp/emp_staff_record_d'
    download(hdfs_path, local_path)
    upload(local_path, hdfs_path)
```

# 3. 测试和生产的Doris DDL
```sql
CREATE TABLE ods.`ods_mkt_dds_sales_monitoring` (
  `id` varchar(64) NULL COMMENT '主键',
  `index_id` varchar(16) NULL COMMENT 'excel对应ID',
  `uuid` varchar(64) NULL COMMENT '业务主键',
  `trip_month` date NULL COMMENT '旅行年月',
  `ticket_type` varchar(64) NULL COMMENT '行程类型',
  `travel_agency_number` varchar(32) NULL COMMENT '代理人IATA码',
  `travel_agency_name` varchar(512) NULL COMMENT '代理人名称',
  `country_of_sale` varchar(8) NULL COMMENT '销售国二字码',
  `source` varchar(64) NULL COMMENT '数据源',
  `gds` varchar(256) NULL COMMENT 'GDS分销系统',
  `ticketing_ai` varchar(8) NULL COMMENT '出票航司二字码',
  `poo_airport` varchar(16) NULL COMMENT '始发地机场三字码',
  `distribution_channel` varchar(16) NULL COMMENT '销售渠道',
  `transaction` varchar(32) NULL COMMENT '业务类型',
  `od_dominant_cabin_class` varchar(128) NULL COMMENT 'O&D主要舱位等级',
  `od_rbkd` varchar(4) NULL COMMENT 'O&D主要子舱段',
  `dominant_marketing_airline` varchar(8) NULL COMMENT '主要销售航司二字码',
  `marketing_airline_1` varchar(8) NULL COMMENT '第一航段销售航司二字码',
  `marketing_airline_2` varchar(16) NULL COMMENT '第二航段销售航司二字码',
  `marketing_airline_3` varchar(16) NULL COMMENT '第三航段销售航司二字码',
  `marketing_airline_4` varchar(16) NULL COMMENT '第四航段销售航司二字码',
  `marketing_airline_5` varchar(16) NULL COMMENT '第五航段销售航司二字码',
  `marketing_airline_6` varchar(16) NULL COMMENT '第六航段销售航司二字码',
  `dominant_operating_airline` varchar(8) NULL COMMENT '主要执飞航司二字码',
  `operating_airline_1` varchar(8) NULL COMMENT '第一航段执飞航司二字码',
  `operating_airline_2` varchar(16) NULL COMMENT '第二航段执飞航司二字码',
  `operating_airline_3` varchar(16) NULL COMMENT '第三航段执飞航司二字码',
  `operating_airline_4` varchar(16) NULL COMMENT '第四航段执飞航司二字码',
  `operating_airline_5` varchar(16) NULL COMMENT '第五航段执飞航司二字码',
  `operating_airline_6` varchar(16) NULL COMMENT '第六航段执飞航司二字码',
  `origin` varchar(16) NULL COMMENT '出发机场三字码',
  `trip_origin_city` varchar(64) NULL COMMENT '出发地城市名称',
  `trip_origin_country_code` varchar(8) NULL COMMENT '出发地国家二字码',
  `trip_origin_country_name` varchar(128) NULL COMMENT '出发地国家名称',
  `trip_origin_region` varchar(32) NULL COMMENT '出发地大洲名称',
  `stop_1` varchar(16) NULL COMMENT '第一经停点机场三字码',
  `stop_2` varchar(16) NULL COMMENT '第二经停点机场三字码',
  `stop_3` varchar(16) NULL COMMENT '第三经停点机场三字码',
  `stop_4` varchar(16) NULL COMMENT '第四经停点机场三字码',
  `stop_5` varchar(16) NULL COMMENT '第五经停点机场三字码',
  `stop_6` varchar(16) NULL COMMENT '第六经停点机场三字码',
  `destination` varchar(16) NULL COMMENT '到达机场三字码',
  `trip_destination_city` varchar(64) NULL COMMENT '到达地城市名称',
  `trip_destination_country_code` varchar(8) NULL COMMENT '到达地国家二字码',
  `trip_destination_country_name` varchar(64) NULL COMMENT '到达地国家名称',
  `trip_destination_region` varchar(32) NULL COMMENT '到达地国家大洲',
  `non_stop_and_connecting_indicator` varchar(32) NULL COMMENT '直飞经停指示',
  `od_days_sold_prior_to_travel` varchar(16) NULL COMMENT '订票提前期（天）',
  `duration_minutes` varchar(32) NULL COMMENT '旅行时间（分钟）',
  `distance` varchar(16) NULL COMMENT '航距（km）',
  `pax` varchar(16) NULL COMMENT '旅客人数',
  `blended_average_fare` varchar(16) NULL COMMENT '平均票价（元）',
  `blended_revenue` varchar(32) NULL COMMENT '销售额（元）',
  `blended_payment_amount` varchar(16) NULL COMMENT '平均旅客支付价格（元）',
  `csv_file_name` varchar(64) NULL COMMENT 'excel 名称',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
UNIQUE KEY(`id`, `index_id`)
COMMENT 'DDS销售监控报表'
DISTRIBUTED BY HASH(`id`) BUCKETS AUTO
PROPERTIES (
"replication_allocation" = "tag.location.group_a: 1, tag.location.group_c: 1, tag.location.group_b: 1"
);
```

# 4. 测试和生产的Hive DDL

```sql

CREATE TABLE ods.`ods_mkt_dds_sales_monitoring` (
  `id` String,
  `index_id` String,
  `uuid` String,
  `trip_month` DATE,
  `ticket_type` String,
  `travel_agency_number` String,
  `travel_agency_name` String,
  `country_of_sale` String,
  `source` String,
  `gds` String,
  `ticketing_ai` String,
  `poo_airport` String,
  `distribution_channel` String,
  `transaction` String,
  `od_dominant_cabin_class` String,
  `od_rbkd` String,
  `dominant_marketing_airline` String,
  `marketing_airline_1` String,
  `marketing_airline_2` String,
  `marketing_airline_3` String,
  `marketing_airline_4` String,
  `marketing_airline_5` String,
  `marketing_airline_6` String,
  `dominant_operating_airline` String,
  `operating_airline_1` String,
  `operating_airline_2` String,
  `operating_airline_3` String,
  `operating_airline_4` String,
  `operating_airline_5` String,
  `operating_airline_6` String,
  `origin` String,
  `trip_origin_city` String,
  `trip_origin_country_code` String,
  `trip_origin_country_name` String,
  `trip_origin_region` String,
  `stop_1` String,
  `stop_2` String,
  `stop_3` String,
  `stop_4` String,
  `stop_5` String,
  `stop_6` String,
  `destination` String,
  `trip_destination_city` String,
  `trip_destination_country_code` String,
  `trip_destination_country_name` String,
  `trip_destination_region` String,
  `non_stop_and_connecting_indicator` String,
  `od_days_sold_prior_to_travel` String,
  `duration_minutes` String,
  `distance` String,
  `pax` String,
  `blended_average_fare` String,
  `blended_revenue` String,
  `blended_payment_amount` String,
  `csv_file_name` String,
  `gmt_create` TIMESTAMP,
  `gmt_modified` TIMESTAMP
) ;
```

# 5. 编写hive2doris脚本
```sql
-- hvie2doris
CREATE CATALOG hive_catalog WITH (
    'type' = 'hive',
    'default-database' = 'ods',
    'hive-conf-dir' = '/opt/cloudera/parcels/CDH-6.2.1-1.cdh6.2.1.p0.1425774/lib/hive/conf'
);

CREATE TABLE mysql_ods_mkt_dds_sales_monitoring(
`id` STRING,
`index_id` STRING,
`uuid` STRING,
`trip_month` DATE,
`ticket_type` STRING,
`travel_agency_number` STRING,
`travel_agency_name` STRING,
`country_of_sale` STRING,
`source` STRING,
`gds` STRING,
`ticketing_ai` STRING,
`poo_airport` STRING,
`distribution_channel` STRING,
`transaction` STRING,
`od_dominant_cabin_class` STRING,
`od_rbkd` STRING,
`dominant_marketing_airline` STRING,
`marketing_airline_1` STRING,
`marketing_airline_2` STRING,
`marketing_airline_3` STRING,
`marketing_airline_4` STRING,
`marketing_airline_5` STRING,
`marketing_airline_6` STRING,
`dominant_operating_airline` STRING,
`operating_airline_1` STRING,
`operating_airline_2` STRING,
`operating_airline_3` STRING,
`operating_airline_4` STRING,
`operating_airline_5` STRING,
`operating_airline_6` STRING,
`origin` STRING,
`trip_origin_city` STRING,
`trip_origin_country_code` STRING,
`trip_origin_country_name` STRING,
`trip_origin_region` STRING,
`stop_1` STRING,
`stop_2` STRING,
`stop_3` STRING,
`stop_4` STRING,
`stop_5` STRING,
`stop_6` STRING,
`destination` STRING,
`trip_destination_city` STRING,
`trip_destination_country_code` STRING,
`trip_destination_country_name` STRING,
`trip_destination_region` STRING,
`non_stop_and_connecting_indicator` STRING,
`od_days_sold_prior_to_travel` STRING,
`duration_minutes` STRING,
`distance` STRING,
`pax` STRING,
`blended_average_fare` STRING,
`blended_revenue` STRING,
`blended_payment_amount` STRING,
`csv_file_name` STRING,
`gmt_create` TIMESTAMP(3),
`gmt_modified` TIMESTAMP(3),
primary key (id,index_id) NOT ENFORCED
) WITH (
    'connector'              = 'doris',
    'fenodes'                = '************:8030,************:8030,************:8030',
    'username'               = 'juneyaoair_release',
    'password'               = 'daNmf_Fgf_642',
    'table.identifier'       = 'ods.ods_mkt_dds_sales_monitoring',
    'sink.enable.batch-mode' = 'true'
);

insert overwrite hive_catalog.ods.ods_mkt_dds_sales_monitoring
select
CAST(`id` AS STRING) as id,
CAST(`index_id` AS STRING) as index_id,
CAST(`uuid` AS STRING) as uuid,
CAST(`trip_month` AS DATE) as trip_month,
CAST(`ticket_type` AS STRING) as ticket_type,
CAST(`travel_agency_number` AS STRING) as travel_agency_number,
CAST(`travel_agency_name` AS STRING) as travel_agency_name,
CAST(`country_of_sale` AS STRING) as country_of_sale,
CAST(`source` AS STRING) as source,
CAST(`gds` AS STRING) as gds,
CAST(`ticketing_ai` AS STRING) as ticketing_ai,
CAST(`poo_airport` AS STRING) as poo_airport,
CAST(`distribution_channel` AS STRING) as distribution_channel,
CAST(`transaction` AS STRING) as transaction,
CAST(`od_dominant_cabin_class` AS STRING) as od_dominant_cabin_class,
CAST(`od_rbkd` AS STRING) as od_rbkd,
CAST(`dominant_marketing_airline` AS STRING) as dominant_marketing_airline,
CAST(`marketing_airline_1` AS STRING) as marketing_airline_1,
CAST(`marketing_airline_2` AS STRING) as marketing_airline_2,
CAST(`marketing_airline_3` AS STRING) as marketing_airline_3,
CAST(`marketing_airline_4` AS STRING) as marketing_airline_4,
CAST(`marketing_airline_5` AS STRING) as marketing_airline_5,
CAST(`marketing_airline_6` AS STRING) as marketing_airline_6,
CAST(`dominant_operating_airline` AS STRING) as dominant_operating_airline,
CAST(`operating_airline_1` AS STRING) as operating_airline_1,
CAST(`operating_airline_2` AS STRING) as operating_airline_2,
CAST(`operating_airline_3` AS STRING) as operating_airline_3,
CAST(`operating_airline_4` AS STRING) as operating_airline_4,
CAST(`operating_airline_5` AS STRING) as operating_airline_5,
CAST(`operating_airline_6` AS STRING) as operating_airline_6,
CAST(`origin` AS STRING) as origin,
CAST(`trip_origin_city` AS STRING) as trip_origin_city,
CAST(`trip_origin_country_code` AS STRING) as trip_origin_country_code,
CAST(`trip_origin_country_name` AS STRING) as trip_origin_country_name,
CAST(`trip_origin_region` AS STRING) as trip_origin_region,
CAST(`stop_1` AS STRING) as stop_1,
CAST(`stop_2` AS STRING) as stop_2,
CAST(`stop_3` AS STRING) as stop_3,
CAST(`stop_4` AS STRING) as stop_4,
CAST(`stop_5` AS STRING) as stop_5,
CAST(`stop_6` AS STRING) as stop_6,
CAST(`destination` AS STRING) as destination,
CAST(`trip_destination_city` AS STRING) as trip_destination_city,
CAST(`trip_destination_country_code` AS STRING) as trip_destination_country_code,
CAST(`trip_destination_country_name` AS STRING) as trip_destination_country_name,
CAST(`trip_destination_region` AS STRING) as trip_destination_region,
CAST(`non_stop_and_connecting_indicator` AS STRING) as non_stop_and_connecting_indicator,
CAST(`od_days_sold_prior_to_travel` AS STRING) as od_days_sold_prior_to_travel,
CAST(`duration_minutes` AS STRING) as duration_minutes,
CAST(`distance` AS STRING) as distance,
CAST(`pax` AS STRING) as pax,
CAST(`blended_average_fare` AS STRING) as blended_average_fare,
CAST(`blended_revenue` AS STRING) as blended_revenue,
CAST(`blended_payment_amount` AS STRING) as blended_payment_amount,
CAST(`csv_file_name` AS STRING) as csv_file_name,
now() as gmt_create,
now() as gmt_modified
from
mysql_ods_mkt_dds_sales_monitoring
;
```


# 6. 编写doris2hive脚本

```sql

create catalog hive_catalog with (
    'type' = 'hive',
    'default-database' = 'ods',
    'hive-conf-dir' = '/opt/cloudera/parcels/CDH-6.2.1-1.cdh6.2.1.p0.1425774/lib/hive/conf'
);
set table.exec.hive.infer-source-parallelism.max=1;

CREATE TABLE mysql_ods_mkt_dds_sales_monitoring(
`id` STRING,
`index_id` STRING,
`uuid` STRING,
`trip_month` DATE,
`ticket_type` STRING,
`travel_agency_number` STRING,
`travel_agency_name` STRING,
`country_of_sale` STRING,
`source` STRING,
`gds` STRING,
`ticketing_ai` STRING,
`poo_airport` STRING,
`distribution_channel` STRING,
`transaction` STRING,
`od_dominant_cabin_class` STRING,
`od_rbkd` STRING,
`dominant_marketing_airline` STRING,
`marketing_airline_1` STRING,
`marketing_airline_2` STRING,
`marketing_airline_3` STRING,
`marketing_airline_4` STRING,
`marketing_airline_5` STRING,
`marketing_airline_6` STRING,
`dominant_operating_airline` STRING,
`operating_airline_1` STRING,
`operating_airline_2` STRING,
`operating_airline_3` STRING,
`operating_airline_4` STRING,
`operating_airline_5` STRING,
`operating_airline_6` STRING,
`origin` STRING,
`trip_origin_city` STRING,
`trip_origin_country_code` STRING,
`trip_origin_country_name` STRING,
`trip_origin_region` STRING,
`stop_1` STRING,
`stop_2` STRING,
`stop_3` STRING,
`stop_4` STRING,
`stop_5` STRING,
`stop_6` STRING,
`destination` STRING,
`trip_destination_city` STRING,
`trip_destination_country_code` STRING,
`trip_destination_country_name` STRING,
`trip_destination_region` STRING,
`non_stop_and_connecting_indicator` STRING,
`od_days_sold_prior_to_travel` STRING,
`duration_minutes` STRING,
`distance` STRING,
`pax` STRING,
`blended_average_fare` STRING,
`blended_revenue` STRING,
`blended_payment_amount` STRING,
`csv_file_name` STRING,
`gmt_create` TIMESTAMP(3),
`gmt_modified` TIMESTAMP(3),
primary key (id,index_id) NOT ENFORCED
) with (
    'connector' = 'doris',
    'fenodes' = '***********:8030',
    'table.identifier' = 'ods.ods_mkt_dds_sales_monitoring',
    'username' = 'juneyaoair_etl',
    'password' = 'juneyaoair_etl123!'
);


insert into mysql_ods_mkt_dds_sales_monitoring
select
CAST(`id` AS STRING) as id,
CAST(`index_id` AS STRING) as index_id,
CAST(`uuid` AS STRING) as uuid,
CAST(`trip_month` AS DATE) as trip_month,
CAST(`ticket_type` AS STRING) as ticket_type,
CAST(`travel_agency_number` AS STRING) as travel_agency_number,
CAST(`travel_agency_name` AS STRING) as travel_agency_name,
CAST(`country_of_sale` AS STRING) as country_of_sale,
CAST(`source` AS STRING) as source,
CAST(`gds` AS STRING) as gds,
CAST(`ticketing_ai` AS STRING) as ticketing_ai,
CAST(`poo_airport` AS STRING) as poo_airport,
CAST(`distribution_channel` AS STRING) as distribution_channel,
CAST(`transaction` AS STRING) as transaction,
CAST(`od_dominant_cabin_class` AS STRING) as od_dominant_cabin_class,
CAST(`od_rbkd` AS STRING) as od_rbkd,
CAST(`dominant_marketing_airline` AS STRING) as dominant_marketing_airline,
CAST(`marketing_airline_1` AS STRING) as marketing_airline_1,
CAST(`marketing_airline_2` AS STRING) as marketing_airline_2,
CAST(`marketing_airline_3` AS STRING) as marketing_airline_3,
CAST(`marketing_airline_4` AS STRING) as marketing_airline_4,
CAST(`marketing_airline_5` AS STRING) as marketing_airline_5,
CAST(`marketing_airline_6` AS STRING) as marketing_airline_6,
CAST(`dominant_operating_airline` AS STRING) as dominant_operating_airline,
CAST(`operating_airline_1` AS STRING) as operating_airline_1,
CAST(`operating_airline_2` AS STRING) as operating_airline_2,
CAST(`operating_airline_3` AS STRING) as operating_airline_3,
CAST(`operating_airline_4` AS STRING) as operating_airline_4,
CAST(`operating_airline_5` AS STRING) as operating_airline_5,
CAST(`operating_airline_6` AS STRING) as operating_airline_6,
CAST(`origin` AS STRING) as origin,
CAST(`trip_origin_city` AS STRING) as trip_origin_city,
CAST(`trip_origin_country_code` AS STRING) as trip_origin_country_code,
CAST(`trip_origin_country_name` AS STRING) as trip_origin_country_name,
CAST(`trip_origin_region` AS STRING) as trip_origin_region,
CAST(`stop_1` AS STRING) as stop_1,
CAST(`stop_2` AS STRING) as stop_2,
CAST(`stop_3` AS STRING) as stop_3,
CAST(`stop_4` AS STRING) as stop_4,
CAST(`stop_5` AS STRING) as stop_5,
CAST(`stop_6` AS STRING) as stop_6,
CAST(`destination` AS STRING) as destination,
CAST(`trip_destination_city` AS STRING) as trip_destination_city,
CAST(`trip_destination_country_code` AS STRING) as trip_destination_country_code,
CAST(`trip_destination_country_name` AS STRING) as trip_destination_country_name,
CAST(`trip_destination_region` AS STRING) as trip_destination_region,
CAST(`non_stop_and_connecting_indicator` AS STRING) as non_stop_and_connecting_indicator,
CAST(`od_days_sold_prior_to_travel` AS STRING) as od_days_sold_prior_to_travel,
CAST(`duration_minutes` AS STRING) as duration_minutes,
CAST(`distance` AS STRING) as distance,
CAST(`pax` AS STRING) as pax,
CAST(`blended_average_fare` AS STRING) as blended_average_fare,
CAST(`blended_revenue` AS STRING) as blended_revenue,
CAST(`blended_payment_amount` AS STRING) as blended_payment_amount,
CAST(`csv_file_name` AS STRING) as csv_file_name,
now() as gmt_create,
now() as gmt_modified
from hive_catalog.ods.ods_mkt_dds_sales_monitoring
;


```