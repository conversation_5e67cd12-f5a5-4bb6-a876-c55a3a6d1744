
#!/bin/bash
source mda/dds/config/common.sh

opts=("$@") 
echo $opts

n=${#opts[@]}

if [ $n -eq 0 ]; then
    echo "错误：未提供任何参数"
    exit 1
fi

env=stg
python_path=/opt/app/dds/.venv/bin/python
ssh_user_name=root
ssh_ip=***********
#env=prd
#python_path=/opt/app/python3/bin/python3.8
#ssh_user_name=fdap
#ssh_ip=************
#ssh_ip=************

echo "[INFO] env: "${env}
echo "[INFO] python_path: "${python_path}
echo "[INFO] ssh_user_name: "${ssh_user_name}
echo "[INFO] ssh_ip: "${ssh_ip}


project_base_path='/opt/app/dds'
echo "[INFO] PROJECT_BASE_PATH: "${project_base_path}

python_script_name="${opts[$n-1]}"
echo "[INFO] PYTHON_SCRIPT_NAME: "${python_script_name}

echo "[INFO] COMMON_JSON: "${common_json}

# 或者不带换行（-w 0）
common_json_str_bs64_nowrap=$(echo -n "$common_json" | base64 -w 0)

function run_python_script() {
    ret=0
    ssh ${ssh_user_name}@${ssh_ip} "${python_path} ${project_base_path}/${python_script_name} ${common_json_str_bs64_nowrap}"  2>&1
    ret=$?
    echo "ret is: $ret"
    if ((ret != 0)); then
    msg="${2}执行失败"
    echo $msg
    exit 1
    else
    echo "${2}执行成功"
    fi
    exit 0

}

run_python_script