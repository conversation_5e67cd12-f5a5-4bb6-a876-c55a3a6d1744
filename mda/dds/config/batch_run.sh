#!/bin/bash

job_index=(12 13 14 15 16 17 18 19 21 22 23 24 25 26 27 28 29 31 32 37 41 42 43 51 52 61 62 71 72 73 74 78 81 82 87 91 92)

for i in ${job_index[@]}; do
    echo "Processing job index: $i"
    
    # 检查配置文件是否存在
    config_file="mda/dds/sh_home/report_sample_${i}.json"
    if [ ! -f "$config_file" ]; then
        echo "Warning: Config file $config_file not found, skipping..."
        continue
    fi
    
    # 执行任务
    sh mda/dds/sh_home/dds_run.sh "$(cat $config_file)" "$i" ${s_year} ${s_month} ${s_day} ${s_year} ${s_month} $(date -d "${s_year_month}01 + 1 month - 1 day" +%d) dds_job.py
    
    # 检查执行结果
    if [ $? -eq 0 ]; then
        echo "Job $i completed successfully"
    else
        echo "Job $i failed with exit code $?"
    fi
    
    echo "----------------------------------------"
done

echo "All jobs completed"


