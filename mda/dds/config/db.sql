drop table `mda_dds_login_tokens`
CREATE TABLE `mda_dds_login_tokens` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '业务主键',
  `token` varchar(2000) NOT NULL COMMENT '登录凭证',
  `is_delete` int NOT NULL COMMENT '是否删除',
  `insert_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
  `create_user`  varchar(50) NOT NULL COMMENT '创建人',
  `update_user` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='dds认证表'

drop table if exists ods.ods_mkt_dds_sales_monitoring;
CREATE TABLE ods.ods_mkt_dds_sales_monitoring (
 `id` VARCHAR(64) COMMENT '主键'
,`index_id` VARCHAR(16) COMMENT 'excel对应ID'
,`uuid` VARCHAR(64) COMMENT '业务主键'
,`trip_month` date COMMENT '旅行年月'
,`ticket_type` VARCHAR(64) COMMENT '行程类型'
,`travel_agency_number` VARCHAR(32) COMMENT '代理人IATA码'
,`travel_agency_name` VARCHAR(512) COMMENT '代理人名称'
,`country_of_sale` VARCHAR(8) COMMENT '销售国二字码'
,`source` VARCHAR(64) COMMENT '数据源'
,`gds` VARCHAR(256) COMMENT 'GDS分销系统'
,`ticketing_ai` VARCHAR(8) COMMENT '出票航司二字码'
,`poo_airport` VARCHAR(16) COMMENT '始发地机场三字码'
,`distribution_channel` VARCHAR(16) COMMENT '销售渠道'
,`transaction` VARCHAR(32) COMMENT '业务类型'
,`od_dominant_cabin_class` VARCHAR(128) COMMENT 'O&D主要舱位等级'
,`od_rbkd` VARCHAR(4) COMMENT 'O&D主要子舱段'
,`dominant_marketing_airline` VARCHAR(8) COMMENT '主要销售航司二字码'
,`marketing_airline_1` VARCHAR(8) COMMENT '第一航段销售航司二字码'
,`marketing_airline_2` VARCHAR(16) COMMENT '第二航段销售航司二字码'
,`marketing_airline_3` VARCHAR(16) COMMENT '第三航段销售航司二字码'
,`marketing_airline_4` VARCHAR(16) COMMENT '第四航段销售航司二字码'
,`marketing_airline_5` VARCHAR(16) COMMENT '第五航段销售航司二字码'
,`marketing_airline_6` VARCHAR(16) COMMENT '第六航段销售航司二字码'
,`dominant_operating_airline` VARCHAR(8) COMMENT '主要执飞航司二字码'
,`operating_airline_1` VARCHAR(8) COMMENT '第一航段执飞航司二字码'
,`operating_airline_2` VARCHAR(16) COMMENT '第二航段执飞航司二字码'
,`operating_airline_3` VARCHAR(16) COMMENT '第三航段执飞航司二字码'
,`operating_airline_4` VARCHAR(16) COMMENT '第四航段执飞航司二字码'
,`operating_airline_5` VARCHAR(16) COMMENT '第五航段执飞航司二字码'
,`operating_airline_6` VARCHAR(16) COMMENT '第六航段执飞航司二字码'
,`origin` VARCHAR(16) COMMENT '出发机场三字码'
,`trip_origin_city` VARCHAR(64) COMMENT '出发地城市名称'
,`trip_origin_country_code` VARCHAR(8) COMMENT '出发地国家二字码'
,`trip_origin_country_name` VARCHAR(128) COMMENT '出发地国家名称'
,`trip_origin_region` VARCHAR(32) COMMENT '出发地大洲名称'
,`stop_1` VARCHAR(16) COMMENT '第一经停点机场三字码'
,`stop_2` VARCHAR(16) COMMENT '第二经停点机场三字码'
,`stop_3` VARCHAR(16) COMMENT '第三经停点机场三字码'
,`stop_4` VARCHAR(16) COMMENT '第四经停点机场三字码'
,`stop_5` VARCHAR(16) COMMENT '第五经停点机场三字码'
,`stop_6` VARCHAR(16) COMMENT '第六经停点机场三字码'
,`destination` VARCHAR(16) COMMENT '到达机场三字码'
,`trip_destination_city` VARCHAR(64) COMMENT '到达地城市名称'
,`trip_destination_country_code` VARCHAR(8) COMMENT '到达地国家二字码'
,`trip_destination_country_name` VARCHAR(64) COMMENT '到达地国家名称'
,`trip_destination_region` VARCHAR(32) COMMENT '到达地国家大洲'
,`non_stop_and_connecting_indicator` VARCHAR(32) COMMENT '直飞经停指示'
,`od_days_sold_prior_to_travel` VARCHAR(16) COMMENT '订票提前期（天）'
,`duration_minutes` VARCHAR(32) COMMENT '旅行时间（分钟）'
,`distance` VARCHAR(16) COMMENT '航距（km）'
,`pax` VARCHAR(16) COMMENT '旅客人数'
,`blended_average_fare` VARCHAR(16) COMMENT '平均票价（元）'
,`blended_revenue` VARCHAR(32) COMMENT '销售额（元）'
,`blended_payment_amount` VARCHAR(16) COMMENT '平均旅客支付价格（元）'
,`csv_file_name` VARCHAR(64) COMMENT 'excel 名称'
,`gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
,`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
UNIQUE KEY(`id`,`index_id`)
COMMENT 'DDS销售监控报表'
DISTRIBUTED BY HASH(`id`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.group_a: 1, tag.location.group_c: 1, tag.location.group_b: 1"
);


drop table if exists ods.ods_mkt_dds_sales_monitoring_param;
CREATE TABLE ods.ods_mkt_dds_sales_monitoring_param (
 `id` VARCHAR(64) COMMENT '主键'
,`csv_file_name` VARCHAR(64) COMMENT 'excel 名称'
,`saved_configuration` TEXT COMMENT '提交配置JSON'
,`gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
,`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT 'DDS销售监控报表参数表'
DISTRIBUTED BY HASH(`id`) BUCKETS AUTO
PROPERTIES (
    "replication_allocation" = "tag.location.group_a: 1, tag.location.group_c: 1, tag.location.group_b: 1"
);




