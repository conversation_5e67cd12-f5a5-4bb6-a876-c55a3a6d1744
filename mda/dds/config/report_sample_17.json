{"pivotTableLayout": "staticLayout", "reportLayout": {"column": {}, "row": {"tripFields": {"serviceClass": true, "rbkd": true, "marketingAirline": {"perUserPreference": false, "code": true, "name": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false}, "allMarketingAirlines": true, "operatingAirline": {"perUserPreference": false, "code": true, "name": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false}, "allOperatingAirlines": true, "tripMarket": false, "domIntTrip": false, "origin": {"perUserPreference": false, "code": true, "name": false, "city": true, "state": false, "countryCode": true, "country": true, "region": true, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false, "regionOnlyNonOverlapping": false, "regionNonOverlappingOverlapping": false}, "connectPoints": true, "destination": {"perUserPreference": false, "code": true, "name": false, "city": true, "state": false, "countryCode": true, "country": true, "region": true, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false, "regionOnlyNonOverlapping": false, "regionNonOverlappingOverlapping": false}, "connectNonStopIndicator": true, "partnershipIndicator": false, "fareFlag": false, "daysSoldPriorToTravel": true, "nightsStayed": false, "duration": true}, "segmentFields": {"fareBasis": false, "couponNumber": false, "serviceClass": false, "rbkd": false, "marketingAirline": {"perUserPreference": false, "code": false, "name": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false}, "operatingAirline": {"perUserPreference": false, "code": false, "name": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false}, "origin": {"perUserPreference": false, "code": false, "name": false, "city": false, "state": false, "countryCode": false, "country": false, "region": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false, "regionOnlyNonOverlapping": false, "regionNonOverlappingOverlapping": false}, "destination": {"perUserPreference": false, "code": false, "name": false, "city": false, "state": false, "countryCode": false, "country": false, "region": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false, "regionOnlyNonOverlapping": false, "regionNonOverlappingOverlapping": false}, "marketingFlightNumber": false, "operatingFlightNumber": false, "equipment": false, "localDepartureTime": {"departureTime": false, "hourOfDeparture": false, "departureTime3Hr": false, "departureTime6Hr": false}, "localArrivalTime": {"arrivalTime": false, "arrivalDate": false}, "utcDepartureTimeDate": false, "utcArrivalTimeDate": false, "stopoverFlag": false, "localVsBehindBeyond": false}, "ticketFields": {"visitor": false, "inNdc": false, "cdNdc": false, "ticketType": true, "travelAgency": {"agencyNumber": true, "agencyName": true, "tradingName": false, "agencyType": false, "address": false, "email": false, "phoneNumber": false, "city": false, "state": false, "countryCode": false, "country": false, "region": false, "postalCode": false, "nationalOfficeId": false, "nationalOfficeName": false, "globalOfficeId": false, "globalOfficeName": false, "gboOnlyNonOverlapping": false, "gboNonOverlappingOverlapping": false, "gnoOnlyNonOverlapping": false, "gnoNonOverlappingOverlapping": false, "ggoOnlyNonOverlapping": false, "ggoNonOverlappingOverlapping": false}, "countryOfSale": {"code": true, "name": false, "rgOnlyNonOverlapping": false, "rgNonOverlappingOverlapping": false}, "dataSource": true, "gds": true, "reportingSystemIndicator": false, "ticketingAirline": {"perUserPreference": false, "code": true, "name": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false}, "pooAirline": {"perUserPreference": false, "code": false, "name": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false}, "pooAirport": {"perUserPreference": false, "code": true, "name": false, "city": false, "state": false, "country": false, "region": false, "onlyNonOverlapping": false, "nonOverlappingOverlapping": false, "regionOnlyNonOverlapping": false, "regionNonOverlappingOverlapping": false}, "allMarketingAirlines": false, "allOperatingAirlines": false, "allTripOds": false, "allCoupons": false, "eTicketFlag": false, "domIntTkt": false, "partnershipIndicator": false, "paymentCurrency": false, "paymentExchangeRates": {"usd": false, "eur": false, "gbp": false, "cny": false, "jpy": false, "rub": false, "cad": false, "brl": false, "aud": false, "inr": false, "krw": false}, "ticketDocumentNumber": false, "distributionChannel": true, "detailsForExchangesRefunds": {"transactionCode": true, "originalIssueInformation": false, "previousIssuesInformation": false, "exchangeRefundedCouponNumbers": false}, "populatedForTicketsYourAirlinePlated": {"pnrCode": false, "tourCode": false}}, "datePeriodFields": {"matchInput": false, "ticketPurchasePeriod": {"purchaseDate": false, "purchaseMonthMMMyyyy": false, "purchaseMonthMM": false, "purchaseQuarterQyyyy": false, "purchaseQuaterQ": false, "purchaseYear": false}, "tripPeriod": {"date": false, "dateOfTheWeek": false, "numberOfTheWeek": false, "monthMMMyyyy": true, "monthMM": false, "quarterQyyyy": false, "quarterQ": false, "year": false}, "segmentPeriod": {"date": false, "dateOfTheWeek": false, "numberOfTheWeek": false, "monthMMMyyyy": false, "monthMM": false, "quarterQyyyy": false, "quarterQ": false, "year": false}, "ticketPeriod": {"date": false, "dateOfTheWeek": false, "numberOfTheWeek": false, "monthMMMyyyy": false, "monthMM": false, "quarterQyyyy": false, "quarterQ": false, "year": false}}}, "data": {"count": false, "pax": true, "paxSharePct": false, "loyaltyPax": false, "blendedFareBreakdown": false, "revenueKpi": {"blendedAverageFare": true, "actualFare": false, "industryFare": false, "blendedRevenueCe": true, "revenueShare": false, "actualRevenue": false, "industryRevenue": false, "blendedYield": false, "actualYield": false, "industryYield": false, "loyaltyRevenue": false}, "paymentKpi": {"blendedPaymentAmount": true, "actualPaymentAmount": false, "industryPaymentAmount": false, "blendedPayment": false, "actualPayment": false, "industryPayment": false, "blendedPaymentAmountYield": false, "actualPaymentAmountYield": false, "industryPaymentAmountYield": false}, "rpk": false, "odDistance": true, "adjustedFareKpi": {"adjustedFare": false, "adjustedRevenue": false, "adjustedYield": false, "adjustedPaymentAmount": false, "adjustedPayment": false, "adjustedPaymentAmountYield": false}, "valuesPopulatedForTicketsYourAirlinePlated": {"netRevenue": {"amNetRevenue": false, "avgAmNetRevenue": false}, "commission": {"amCommission": false, "avgCommission": false}, "supplementaryAmount": {"amSupplementary": false, "avgSupplementary": false}, "taxesAndFees": {"totalTaxesAndFees": false, "averageTaxesAndFees": false, "totalPenaltyAmount": false, "averagePenaltyAmount": false, "totalYrAmount": false, "averageYrAmount": false, "totalYqAmount": false, "averageYqAmount": false}}}, "dateChipType": "dateInColumns"}, "general": {"travelPeriod": {"dateTypePivot": "trip", "layout": "column", "timePeriod": "absolute", "periodType": {"rangeOfDates": {"from": "${period_s_date}", "to": "${period_e_date}", "fromDisplay": "${period_s_date_formatted}", "toDisplay": "${period_e_date_formatted}"}}}, "purchasePeriod": {"layout": "column", "timePeriod": "absolute", "periodType": {}}, "airlineDetails": {"excludeTicketing": false, "excludePooMarketing": false, "ticketing": [], "pooMarketing": []}, "otherDetails": {"directionality": "directional", "dataSource": [], "countryOfSale": [], "poo": [], "excludePoo": false}, "agencyDetails": {"excludeCategory": {"categoryType": "", "category": []}, "chipType": "branchOffice", "categoryType": "city", "category": [], "name": [], "excludeName": []}, "yearToDateSnapshot": false}, "ticket": {"itineraryDetails": {"excludeAnySegmentAirport": false, "anySegmentAirport": []}, "distribution": {"distributionChipType": "both", "distributionChannel": [], "gds": []}, "airlineDetails": {"excludeMarketing": false, "excludeOperating": false, "marketing": [], "operating": []}, "otherDetails": {"pnrCode": {"type": "contains", "code": ""}, "tourCode": {"type": "contains", "code": ""}, "tripType": "all", "partnershipType": [], "eTicketFlag": "", "transactions": [], "ticketNumber": ""}}, "trip": {"tripDetails": {"tripType": "all", "chipsType": "all", "pairs": {"pair": "", "excludePair": ""}, "originDestination": {"origin": [{"name": "People's Republic of China | Sub Region", "code": "CN", "type": "country"}], "destination": [{"code": "", "name": "Europe", "type": "region"}], "exclude": {"origin": [], "destination": []}}, "tripConnections": {"any": true, "first": {"code": [], "exclude": false}, "second": {"code": [], "exclude": false}, "third": {"code": [], "exclude": false}, "fourth": {"code": [], "exclude": false}, "fifth": {"code": [], "exclude": false}}}, "airlineDetails": {"excludeMarketing": false, "excludeOperating": false, "marketingOrder": {"any": true, "first": {"code": [], "exclude": false}, "second": {"code": [], "exclude": false}, "third": {"code": [], "exclude": false}, "fourth": {"code": [], "exclude": false}, "fifth": {"code": [], "exclude": false}, "six": {"code": [], "exclude": false}}, "operatingOrder": {"any": true, "first": {"code": [], "exclude": false}, "second": {"code": [], "exclude": false}, "third": {"code": [], "exclude": false}, "fourth": {"code": [], "exclude": false}, "fifth": {"code": [], "exclude": false}, "six": {"code": [], "exclude": false}}, "marketingAirline": [], "operatingAirline": [], "serviceClass": []}, "otherDetails": {"rbkd": [], "partnershipType": []}}, "segment": {"segmentDetails": {"tripType": "all", "chipsType": "all", "originDestination": {"origin": [], "destination": [], "exclude": {"origin": [], "destination": []}}}, "airlineDetails": {"marketingAirline": [], "excludeMarketing": false, "operatingAirline": [], "excludeOperating": false, "marketingFlightNumber": "", "operatingFlightNumber": "", "serviceClass": []}, "otherDetails": {"equipment": "", "rbkd": [], "segDepartureStartTime": "", "segDepartureEndTime": "", "fareBasis": {"type": "contains", "code": ""}}}, "travelPeriodDisplay": "${period_se_date_formatted}", "pivot": {"column": [], "row": ["tripMonthYr", "deTicketTripType", "travelAgencyNumber", "travelAgencyName", "cdTicketingCountry", "cdSource", "nmAirlineGdsOrganization", "cdTicketingAirline", "cdPooAirport", "deDistributionChannel", "cdTransactionUnified", "cdOdDominantCabinClass", "cdOdRbkd", "cdDomMktAirline", "tktCdAllMarketingAirlines", "cdDomOptAirline", "tktCdAllOperatingAirlines", "origCd", "tripOriginCity", "tripOriginCdCountryRgn", "tripOriginCountryRgn", "tripOriginRegionRgn", "tktCdConnectPoints", "destCd", "tripDestCity", "tripDestCdCountryRgn", "tripDestCountryRgn", "tripDestRegionRgn", "inNonStop", "nbOdDaysSoldPriorToTravel", "nbOdDurationMin"], "data": ["pax", "blendedAverageFare", "blendedRevenueCe", "blendedPaymentAmount", "odDistance"]}}