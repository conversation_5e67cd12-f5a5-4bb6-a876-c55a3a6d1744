#!/bin/bash

common_json='
[
    {
        "max_polling_time_out":3600
        ,"submit_url":"dds-reports/api/request-report"
        ,"get_job_report_url":"system-management/management/systems/user-conveniences/report-histories"
        ,"download_url":"user-conveniences/api/user-conveniences/download/report-histories"
        ,"project_base_path": "/opt/app/dds"
        ,"downloads_path": "downloads"
        ,"n_token_flash_second": 3600
        ,"loop_interval":5
        ,"job_success" : "success"
        ,"login_max_retry_count": 3
        ,"login_retry_interval_second": 5
        ,"chrome_runtime_mem": 4096
        ,"selenium_operate_max_timeout_second": 60
        ,"selenium_step2step_interval_second": 1
        ,"remote_debugging_port" : 9222
        ,"max_connect_timeout_second": 30
        ,"max_read_timeout_second": 2900
        ,"doris_table_name": "ods_mkt_dds_sales_monitoring"
        ,"doris_table_columns": "id,index_id,uuid,trip_month,ticket_type,travel_agency_number,travel_agency_name,country_of_sale,source,gds,ticketing_ai,poo_airport,distribution_channel,transaction,od_dominant_cabin_class,od_rbkd,dominant_marketing_airline,marketing_airline_1,marketing_airline_2,marketing_airline_3,marketing_airline_4,marketing_airline_5,marketing_airline_6,dominant_operating_airline,operating_airline_1,operating_airline_2,operating_airline_3,operating_airline_4,operating_airline_5,operating_airline_6,origin,trip_origin_city,trip_origin_country_code,trip_origin_country_name,trip_origin_region,stop_1,stop_2,stop_3,stop_4,stop_5,stop_6,destination,trip_destination_city,trip_destination_country_code,trip_destination_country_name,trip_destination_region,non_stop_and_connecting_indicator,od_days_sold_prior_to_travel,duration_minutes,distance,pax,blended_average_fare,blended_revenue,blended_payment_amount,csv_file_name"
        ,"doris_table_name_param": "ods_mkt_dds_sales_monitoring_param"
        ,"doris_table_columns_param": "id,csv_file_name,saved_configuration"
    }
]
'