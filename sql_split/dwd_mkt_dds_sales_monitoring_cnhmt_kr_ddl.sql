CREATE TABLE dwd.`dwd_mkt_dds_sales_monitoring_cnhmt_kr` (
  `id` varchar(64) NULL COMMENT '主键',
  `index_id` varchar(16) NULL COMMENT 'excel对应ID',
  `uuid` varchar(64) NULL COMMENT '业务主键',
  `trip_month` date NULL COMMENT '旅行年月',
  `ticket_type` varchar(64) NULL COMMENT '行程类型',
  `travel_agency_number` varchar(32) NULL COMMENT '代理人IATA码',
  `travel_agency_name` varchar(512) NULL COMMENT '代理人名称',
  `country_of_sale` varchar(8) NULL COMMENT '销售国二字码',
  `source` varchar(64) NULL COMMENT '数据源',
  `gds` varchar(256) NULL COMMENT 'GDS分销系统',
  `ticketing_ai` varchar(8) NULL COMMENT '出票航司二字码',
  `poo_airport` varchar(16) NULL COMMENT '始发地机场三字码',
  `distribution_channel` varchar(16) NULL COMMENT '销售渠道',
  `transaction` varchar(32) NULL COMMENT '业务类型',
  `od_dominant_cabin_class` varchar(128) NULL COMMENT 'O&D主要舱位等级',
  `od_rbkd` varchar(4) NULL COMMENT 'O&D主要子舱段',
  `dominant_marketing_airline` varchar(8) NULL COMMENT '主要销售航司二字码',
  `marketing_airline_1` varchar(8) NULL COMMENT '第一航段销售航司二字码',
  `marketing_airline_2` varchar(16) NULL COMMENT '第二航段销售航司二字码',
  `marketing_airline_3` varchar(16) NULL COMMENT '第三航段销售航司二字码',
  `marketing_airline_4` varchar(16) NULL COMMENT '第四航段销售航司二字码',
  `marketing_airline_5` varchar(16) NULL COMMENT '第五航段销售航司二字码',
  `marketing_airline_6` varchar(16) NULL COMMENT '第六航段销售航司二字码',
  `dominant_operating_airline` varchar(8) NULL COMMENT '主要执飞航司二字码',
  `operating_airline_1` varchar(8) NULL COMMENT '第一航段执飞航司二字码',
  `operating_airline_2` varchar(16) NULL COMMENT '第二航段执飞航司二字码',
  `operating_airline_3` varchar(16) NULL COMMENT '第三航段执飞航司二字码',
  `operating_airline_4` varchar(16) NULL COMMENT '第四航段执飞航司二字码',
  `operating_airline_5` varchar(16) NULL COMMENT '第五航段执飞航司二字码',
  `operating_airline_6` varchar(16) NULL COMMENT '第六航段执飞航司二字码',
  `origin` varchar(16) NULL COMMENT '出发机场三字码',
  `trip_origin_city` varchar(64) NULL COMMENT '出发地城市名称',
  `trip_origin_country_code` varchar(8) NULL COMMENT '出发地国家二字码',
  `trip_origin_country_name` varchar(128) NULL COMMENT '出发地国家名称',
  `trip_origin_region` varchar(32) NULL COMMENT '出发地大洲名称',
  `stop_1` varchar(16) NULL COMMENT '第一经停点机场三字码',
  `stop_2` varchar(16) NULL COMMENT '第二经停点机场三字码',
  `stop_3` varchar(16) NULL COMMENT '第三经停点机场三字码',
  `stop_4` varchar(16) NULL COMMENT '第四经停点机场三字码',
  `stop_5` varchar(16) NULL COMMENT '第五经停点机场三字码',
  `stop_6` varchar(16) NULL COMMENT '第六经停点机场三字码',
  `destination` varchar(16) NULL COMMENT '到达机场三字码',
  `trip_destination_city` varchar(64) NULL COMMENT '到达地城市名称',
  `trip_destination_country_code` varchar(8) NULL COMMENT '到达地国家二字码',
  `trip_destination_country_name` varchar(64) NULL COMMENT '到达地国家名称',
  `trip_destination_region` varchar(32) NULL COMMENT '到达地国家大洲',
  `non_stop_and_connecting_indicator` varchar(32) NULL COMMENT '直飞经停指示',
  `od_days_sold_prior_to_travel` varchar(16) NULL COMMENT '订票提前期（天）',
  `duration_minutes` varchar(32) NULL COMMENT '旅行时间（分钟）',
  `distance` varchar(16) NULL COMMENT '航距（km）',
  `pax` varchar(16) NULL COMMENT '旅客人数',
  `blended_average_fare` varchar(16) NULL COMMENT '平均票价（元）',
  `blended_revenue` varchar(32) NULL COMMENT '销售额（元）',
  `blended_payment_amount` varchar(16) NULL COMMENT '平均旅客支付价格（元）',
  `csv_file_name` varchar(64) NULL COMMENT 'excel 名称',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
UNIQUE KEY(`id`, `index_id`)
COMMENT 'DDS销售监控报表_中国（含港澳台）-韩国'
DISTRIBUTED BY HASH(`id`) BUCKETS AUTO
PROPERTIES (
'replication_allocation' = 'tag.location.group_a: 1, tag.location.group_c: 1, tag.location.group_b: 1'
);
