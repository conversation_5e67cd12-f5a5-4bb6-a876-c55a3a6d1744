INSERT OVERWRITE TABLE dwd.dwd_mkt_dds_sales_monitoring_cnhmt_kr
SELECT 
  id,
  index_id,
  uuid,
  trip_month,
  ticket_type,
  travel_agency_number,
  travel_agency_name,
  country_of_sale,
  source,
  gds,
  ticketing_ai,
  poo_airport,
  distribution_channel,
  `transaction`,
  od_dominant_cabin_class,
  od_rbkd,
  dominant_marketing_airline,
  marketing_airline_1,
  marketing_airline_2,
  marketing_airline_3,
  marketing_airline_4,
  marketing_airline_5,
  marketing_airline_6,
  dominant_operating_airline,
  operating_airline_1,
  operating_airline_2,
  operating_airline_3,
  operating_airline_4,
  operating_airline_5,
  operating_airline_6,
  origin,
  trip_origin_city,
  trip_origin_country_code,
  trip_origin_country_name,
  trip_origin_region,
  stop_1,
  stop_2,
  stop_3,
  stop_4,
  stop_5,
  stop_6,
  destination,
  trip_destination_city,
  trip_destination_country_code,
  trip_destination_country_name,
  trip_destination_region,
  non_stop_and_connecting_indicator,
  od_days_sold_prior_to_travel,
  duration_minutes,
  distance,
  pax,
  blended_average_fare,
  blended_revenue,
  blended_payment_amount,
  csv_file_name,
  gmt_create,
  gmt_modified
FROM ods_mkt_dds_sales_monitoring
WHERE substr(csv_file_name,1,2) in ('14','24','41','42');
