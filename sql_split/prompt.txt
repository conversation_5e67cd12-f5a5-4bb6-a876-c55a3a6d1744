在我的目录下面有一段ods_mkt_dds_sales_monitoring.sql 是一个ddl，要求你帮我写一些其它的ddl和dml，要求如下

# 一、 编写DDL
    1.按照ods_mkt_dds_sales_monitoring.sql作为基础模版，表名自动进行替换，创建其它dwd表ddl
    2.按照ods_mkt_dds_sales_monitoring.sql作为基础模版，表字段不变，创建其它dwd表ddl
    3.按照ods_mkt_dds_sales_monitoring.sql作为基础模版，comment要变，创建其它dwd表ddl
    4.表名和comment在下面会提供出来
    5.写出对应的ddl, 以{表名}_ddl.sql命名
```
dwd.dwd_mkt_dds_sales_monitoring_cn_hmt.sql comment 中国大陆-港澳台
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_jp.sql comment 中国（含港澳台）-日本
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_kr.sql comment 中国（含港澳台）-韩国
dwd.dwd_mkt_dds_sales_monitoring_cn_sea.sql comment 中国大陆-东南亚
dwd.dwd_mkt_dds_sales_monitoring_hmt_sea.sql comment 港澳台-东南亚
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_otasia.sql comment 中国（含港澳台）-其他亚洲国家
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_euau.sql comment 中国（含港澳台）-欧洲&澳洲
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_otconti.sql comment 中国（含港澳台）-亚欧澳外其他大洲
dwd.dwd_mkt_dds_sales_monitoring_jpkr_eu.sql comment 日本&韩国-欧洲
dwd.dwd_mkt_dds_sales_monitoring_eu_au.sql comment 欧洲-澳洲（经停中日韩）
```

# 二、 编写dml
    1.以ods_mkt_dds_sales_monitoring为主表，按照where条件进行数据检索，覆盖插入到dwd表中
    2.dwd表名和对应的where条件如下
    3.写出对应的dml，以{表名}_dml.sql命名
```
dwd.dwd_mkt_dds_sales_monitoring_cn_hmt.sql comment 中国大陆-港澳台 where substr(csv_file_name,1,2) in ('12','21','22')
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_jp.sql comment 中国（含港澳台）-日本 where substr(csv_file_name,1,2) in ('13','23','31','32')
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_kr.sql comment 中国（含港澳台）-韩国 where substr(csv_file_name,1,2) in ('14','24','41','42')
dwd.dwd_mkt_dds_sales_monitoring_cn_sea.sql comment 中国大陆-东南亚 where substr(csv_file_name,1,2) in ('15','51')
dwd.dwd_mkt_dds_sales_monitoring_hmt_sea.sql comment 港澳台-东南亚 where substr(csv_file_name,1,2) in ('25','52')
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_otasia.sql comment 中国（含港澳台）-其他亚洲国家 where substr(csv_file_name,1,2) in ('16','26','61','62')
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_euau.sql comment 中国（含港澳台）-欧洲&澳洲 where substr(csv_file_name,1,2) in ('17','18','27','28','71','81','72','82')
dwd.dwd_mkt_dds_sales_monitoring_cnhmt_otconti.sql comment 中国（含港澳台）-亚欧澳外其他大洲 where substr(csv_file_name,1,2) in ('19','29','91','92')
dwd.dwd_mkt_dds_sales_monitoring_jpkr_eu.sql comment 日本&韩国-欧洲 where substr(csv_file_name,1,2) in ('37','47','73','74')
dwd.dwd_mkt_dds_sales_monitoring_eu_au.sql comment 欧洲-澳洲（经停中日韩） where substr(csv_file_name,1,2) in ('78','87')
```


