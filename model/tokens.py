from sqlalchemy import Column, BigInteger, String, DateTime, text, Integer
from sqlalchemy.orm import declarative_base
from model.model_init import init_server_db
from datetime import datetime

BaseModel = declarative_base()

class Tokens(BaseModel):
    __tablename__ = 'mda_dds_login_tokens'
    
    id = Column(BigInteger, primary_key=True)
    token = Column(String(2000),default='', server_default='')
    is_delete = Column(Integer, default='0', server_default='0')
    insert_time = Column(DateTime ,server_default=text("CURRENT_TIMESTAMP"))
    create_user = Column(String(50), default='sys')
    update_user = Column(String(50), default='sys')
    update_time = Column(DateTime ,server_default=text("CURRENT_TIMESTAMP"))
    
    
    def __repr__(self):
        return f'<Tokens(id={self.id},token={self.token},is_delete={self.is_delete},insert_time={self.insert_time},create_user={self.create_user},update_user={self.update_user},update_time={self.update_time})>'
    
    def to_dict(self):
        dicts = self.__dict__
        dicts.pop('_sa_instance_state')
        return dicts

    
if __name__ == '__main__':
    init_server_db(BaseModel)