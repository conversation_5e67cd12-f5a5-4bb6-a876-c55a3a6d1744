
from sqlalchemy import create_engine
from common.nacos_utils import get_config
from common.common_utils import settings

def init_server_db(base_model, common_json:dict):
   
    config_group = common_json['config_group']
    secret_config = common_json['secret_config']
    nacos_base_url = common_json['nacos_base_url']
    
    nacos_config = get_config(config_group,secret_config,nacos_base_url)
    host = nacos_config['mysql'][0]['host']
    port = nacos_config['mysql'][0]['port']
    user = nacos_config['mysql'][0]['user']
    password = nacos_config['mysql'][0]['password']
    database = nacos_config['mysql'][0]['database']
    engine = create_engine('mysql+pymysql://%s:%s@%s:%s/%s' % (user, password, host, port, database))
    base_model.metadata.create_all(engine)
    