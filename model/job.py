from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, text
from model.model_init import init_server_db

BaseModel = declarative_base()

class Job(BaseModel):
    __tablename__ = 't_jobs'
    
    id = Column(BigInteger, primary_key=True)
    job_id = Column(String(32), server_default='', comment='job在平台上对应的ID')
    csv_file_name = Column(String(20), server_default='', comment='下载后对应的csv文件名称')
    saved_configuration_name = Column(String(20), server_default='', comment='job报表名称')
    saved_configuration = Column(String(5000), server_default='', comment='保存配置项')
    status = Column(String(20), server_default='queued', comment='状态')
    invocation_time = Column(String(64), server_default='', comment='提交时间')
    is_delete = Column(Integer, server_default='0', comment='是否删除')
    insert_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='插入时间')
    
    def __repr__(self):
        return f'<Job(id={self.id}, job_id={self.job_id}, saved_configuration_name={self.saved_configuration_name}, status={self.status}, invocation_time={self.invocation_time}, is_delete={self.is_delete}, insert_time={self.insert_time})>'
    
    def to_dict(self):
        return {
             'id' :self.id
            ,'job_id' : self.job_id
            ,'csv_file_name' : self.csv_file_name
            ,'saved_configuration_name' : self.saved_configuration_name
            ,'status' : self.status
            ,'invocation_time' : self.invocation_time
            ,'is_delete' : self.is_delete
            ,'insert_time' : self.insert_time
        }
    
if __name__ == '__main__':
    init_server_db(BaseModel)