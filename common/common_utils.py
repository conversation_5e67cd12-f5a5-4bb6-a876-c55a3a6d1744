# -*- coding: utf-8 -*-
# @Time    : 2024/1/18 3:35 下午
# <AUTHOR> <PERSON><PERSON>
from Crypto.Cipher import AES
import base64
import requests
import logging
import os
import sys

requests.packages.urllib3.disable_warnings()


# 通用配置项
class Settings:
    
    log_level = logging.DEBUG
    nacos_base_url = "http://************:8848"
    config_group = "common"
    secret_config = "dds-python"
    
settings = Settings()

def add_root_path():
    root_dir = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    if root_dir not in sys.path:
        sys.path.append(root_dir)
        print("******** sys.path ********")
        print(sys.path)
        print("")

class EncryptData:
    def __init__(self):

        self.key = "psjduiofnhsychs7".encode('utf-8')
        self.length = AES.block_size
        self.aes = AES.new(self.key, AES.MODE_ECB)
        self.unpad = lambda date: date[0:-ord(date[-1])]

    def pad(self, text):
        count = len(text.encode('utf-8'))
        add = self.length - (count % self.length)
        entext = text + (chr(add) * add)
        return entext

    def encrypt(self, encrData):
        res = self.aes.encrypt(self.pad(encrData).encode("utf8"))
        msg = str(base64.b64encode(res), encoding="utf8")
        return msg

    def decrypt(self, decrData):
        res = base64.decodebytes(decrData.encode("utf8"))
        msg = self.aes.decrypt(res).decode("utf8")
        return self.unpad(msg)