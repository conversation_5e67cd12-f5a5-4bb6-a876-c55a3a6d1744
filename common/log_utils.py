import logging
import inspect

from common.common_utils import settings

class AutoLogger:
    def __init__(self, level=logging.INFO):
        logging.basicConfig(
            format='%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            level=level
        )
        self.level = level

    def _get_caller_name(self):
        """获取调用者的文件名"""
        frame = inspect.currentframe()
        try:
            # 获取调用者的frame（跳过当前方法和_log_with_level）
            caller_frame = frame.f_back.f_back
            filename = caller_frame.f_globals.get('__name__', '__main__')
            return filename
        finally:
            del frame

    def _log_with_level(self, level, message, *args, **kwargs):
        """通用日志方法"""
        caller_name = self._get_caller_name()
        logger = logging.getLogger(caller_name)
        getattr(logger, level)(message, *args, **kwargs)

    def debug(self, message, *args, **kwargs):
        self._log_with_level('debug', message, *args, **kwargs)

    def info(self, message, *args, **kwargs):
        self._log_with_level('info', message, *args, **kwargs)

    def warning(self, message, *args, **kwargs):
        self._log_with_level('warning', message, *args, **kwargs)

    def error(self, message, *args, **kwargs):
        self._log_with_level('error', message, *args, **kwargs)

    def critical(self, message, *args, **kwargs):
        self._log_with_level('critical', message, *args, **kwargs)

    def set_level(self, level=logging.INFO):
        """动态设置日志级别"""
        self.level = level
        logging.getLogger().setLevel(level)
        # 同时更新所有现有的logger
        for logger_name in logging.Logger.manager.loggerDict:
            logging.getLogger(logger_name).setLevel(level)
        
log = AutoLogger(settings.log_level)