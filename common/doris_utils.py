import logging
from typing import Literal
from requests.auth import HTTPBasicAuth
from common.http_utils import send_request
import os
import time
import random


#host = '************'
#user = 'juneyaoair_release'
#password = 'daNmf_Fgf_642'
#database = 'ods'
#table_name = 'ods_mkt_oag_od_report_dev_1'
#columns = 'id,time_series,od_uuid,pub_al_dominant,op_al_dominant,origin,pub_al_leg_1,op_al_leg_1,gateway_1,pub_al_leg_2,op_al_leg_2,gateway_2,pub_al_leg_3,op_al_leg_3,destination,point_of_origin,point_of_sale,cabin,estimated_pax,fare,index_id,period_time_period_label,period_type,period_from_date,period_from_analyser_formatted,period_from_year,period_from_month,period_from_quarter,period_from_season,period_from_message_formatted,period_to_date,period_to_analyser_formatted,period_to_year,period_to_month,period_to_quarter,period_to_season,period_to_message_formatted,period_timeseries,compare_to,selected_flight_type,show_by,to_from_toggle,origin_include_port_type,origin_include_port_list,origin_exclude_port_type,origin_exclude_port_list,gateway1_include_port_type,gateway1_include_port_list,gateway1_exclude_port_type,gateway1_exclude_port_list,gateway2_include_port_type,gateway2_include_port_list,gateway2_exclude_port_type,gateway2_exclude_port_list,destination_include_port_type,destination_include_port_list,destination_exclude_port_type,destination_exclude_port_list,direction_store_ident,direction,connections_operator,connections_number,unserved,flight_type_int_dom,csv_file_name,gmt_create,gmt_modified'

log = logging.getLogger(__name__)
def setup_logger():
    ch = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s [%(levelname).1s] %(pathname)s:%(lineno)d %(message)s")
    ch.setFormatter(formatter)
    if True:
        log.setLevel(logging.DEBUG)
    else:
        log.setLevel(logging.INFO)
    log.addHandler(ch)

def stream_load(
        file: str,
        *,
        host: str,
        user: str,
        password: str,
        database: str,
        table_name: str,
        columns: str,
        merge_type: Literal["DELETE", "APPEND", "MERGE"] = "APPEND",
        format: Literal["parquet", "json", "csv"] = "parquet",
        compress_type: Literal["lz4", "gz", "lzo", "bz2", "lzop", "deflate"] = "lz4",
        timeout=60000,
        max_retries: int = 3,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        **kwargs,
    ) -> int:
        """
        执行流式加载，包含失败自动重试机制
        
        Args:
            file: 要加载的文件路径
            host: 主机名
            user: 用户名
            password: 密码
            database: 数据库名
            table_name: 表名
            columns: 列名
            merge_type: 合并类型
            format: 文件格式
            compress_type: 压缩类型
            timeout: 超时时间(毫秒)
            max_retries: 最大重试次数
            backoff_factor: 退避系数，每次重试等待时间为 backoff_factor * (2 ** retry_count)
            jitter: 是否添加随机抖动以避免多个客户端同时重试
            **kwargs: 其他参数
            
        Returns:
            int: 成功加载的行数，失败返回0
        """
        
        
        if not os.path.exists(file):
            log.error(f"文件不存在: {file}")
            return 0
            
        auth = HTTPBasicAuth(user, password)
        headers = {
            "Expect": "100-continue",
            "format": format,
            "columns": columns,
            "compress_type": compress_type,
            "merge_type": merge_type,
            "timeout": str(timeout),
        }
        if format in ["json", "csv"]:
            headers["Content-Type"] = "text/plain"
        if kwargs.get("column_separator"):
            headers["column_separator"] = kwargs["column_separator"]
        if kwargs.get("line_delimiter"):
            headers["line_delimiter"] = kwargs["line_delimiter"]

        api_url = f"http://{host}:8030/api/{database}/{table_name}/_stream_load"
        
        retry_count = 0
        while retry_count <= max_retries:
            try:
                with open(file, "rb") as f:
                    # 假设 send_request 是已定义的函数
                    resp = send_request("PUT", url=api_url, auth=auth, headers=headers, data=f)
                    
                    if resp.ok:
                        total_rows = resp.json()["NumberTotalRows"]
                        #if int(total_rows) == 0:
                        #    log.info("stream load 响应: %s", resp.json())
                            
                        log.info("stream load 响应: %s", resp.json())
                        log.info(f"写入表{database}.{table_name}数据量: {total_rows}")
                        return int(total_rows)
                    else:
                        # 某些错误码可能表示不需要重试的情况，此处可以针对不同错误码进行处理
                        error_msg = f"stream load 失败: {resp.status_code}, {resp.reason}, {str(resp.content)}"
                        log.error(error_msg)
                        
                        # 如果是最后一次重试，直接返回失败
                        if retry_count == max_retries:
                            return 0
                            
                        # 否则准备重试
                        retry_count += 1
                        wait_time = backoff_factor * (2 ** (retry_count - 1))
                        
                        # 添加随机抖动，避免多个客户端同时重试
                        if jitter:
                            wait_time = wait_time * (0.5 + random.random())
                            
                        log.warning(f"第 {retry_count} 次重试，等待 {wait_time:.2f} 秒后重试...")
                        time.sleep(wait_time)
                        continue
            except Exception as e:
                log.exception(f"stream load 异常: {str(e)}")
                
                # 如果是最后一次重试，直接返回失败
                if retry_count == max_retries:
                    return 0
                    
                # 否则准备重试
                retry_count += 1
                wait_time = backoff_factor * (2 ** (retry_count - 1))
                
                # 添加随机抖动
                if jitter:
                    wait_time = wait_time * (0.5 + random.random())
                    
                log.warning(f"第 {retry_count} 次重试，等待 {wait_time:.2f} 秒后重试...")
                time.sleep(wait_time)
        
        return 0  # 所有重试都失败后返回0
            
            


if __name__ == '__main__':
    setup_logger()
    # stream_load(
    #      file = '/Users/<USER>/workspace/vscode/oag-python/tmp/parse_files/OD0012024041.CSV'
    #     ,host = host
    #     ,user = user
    #     ,password = password
    #     ,database = database
    #     ,table_name = table_name
    #     ,columns = columns
    #     ,format = 'csv'
    #     ,compress_type = ''
    #     ,column_separator = ','
    #     ,max_retries = 3  # 最多重试3次
    #     ,backoff_factor = 1.5  # 退避系数
    # )