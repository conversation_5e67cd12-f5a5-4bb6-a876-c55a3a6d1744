from datetime import datetime 
def get_date(year, month, day):
    return datetime(year, month, day).strftime("%y%m%d")

def get_date_formatted(year, month, day):
    ddate = datetime(year, month, day)
    formatted_date = ddate.strftime("%d %B %Y")
    return formatted_date

# 测试
if __name__ == "__main__":
    year, month, day = 2025, 1, 16
    
    print(f"date: {get_date(year, month, day)}")
    print(f"analyserFormatted: {get_date_formatted(year, month, day)}")
    
    
