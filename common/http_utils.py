from typing import Literal
from requests import Response, Session, Request
from common.log_utils import log

class NoRebuildAuthSession(Session):
    def rebuild_auth(self, prepared_request, response):
        pass

def send_request(
    method: Literal["PUT", "GET", "POST", "DELETE", "PATCH"],
    url: str,
    **kwargs,
) -> Response:
    with NoRebuildAuthSession() as session:
        # This line is intended to block the requests library from accessing proxy config through env variables.
        session.trust_env = False
        try:
            req = Request(
                method=method,
                url=url,
                **kwargs,
            )
            log.debug(f"请求地址: {req.url}")
            prepared = req.prepare()
            resp = session.send(prepared)
            log.debug(f"响应码: {resp.status_code}")
            return resp
        except Exception as e:
            log.warning(f"请求错误: {e}")
            raise e
