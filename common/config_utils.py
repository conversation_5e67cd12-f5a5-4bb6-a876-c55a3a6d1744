import json
import os

from common.date_utils import get_date, get_date_formatted
from typing import Any, Dict


def get_json_config_by_file(file_path, replacements=None):
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"配置文件不存在: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        
        content = content.replace('\n', '')
        content = content.replace(' ', '')
        
        if replacements is not None:
            for placeholder, value in replacements.items():
                content = content.replace(str(placeholder), str(value))
        
        return content
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"配置文件包含无效的JSON格式: {str(e)}", e.doc, e.pos)
    except Exception as e:
        raise Exception(f"读取配置文件时出错: {str(e)}")

def get_config_by_file(s_year, s_month, s_day,e_year, e_month, e_day, config_name):
    replacements = {
         "${period_s_date}": get_date(s_year, s_month, s_day)
        ,"${period_s_date_formatted}": get_date_formatted(s_year, s_month, s_day)
        ,"${period_e_date}": get_date(e_year, e_month, e_day)
        ,"${period_e_date_formatted}": get_date_formatted(e_year, e_month, e_day)
        ,"${period_se_date_formatted}": f'{get_date_formatted(s_year, s_month, s_day)} to {get_date_formatted(e_year, e_month, e_day)}'
    }
    
    return get_json_config_by_file(config_name, replacements=replacements)

def get_json_config_by_jstr(content, replacements=None):
    try:
        content = content.replace('\n', '')
        content = content.replace(' ', '')
        
        if replacements is not None:
            for placeholder, value in replacements.items():
                content = content.replace(str(placeholder), str(value))
        
        return content
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"配置文件包含无效的JSON格式: {str(e)}", e.doc, e.pos)
    except Exception as e:
        raise Exception(f"读取配置文件时出错: {str(e)}")

if __name__ == '__main__':
    
    s_year, s_month, s_day = 2025, 1, 13
    e_year, e_month, e_day = 2025, 1, 15
    result = get_config_by_file(s_year, s_month, s_day, e_year, e_month, e_day,'config/report_sample_1.json')
    print(result)