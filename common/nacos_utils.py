from urllib.parse import urljoin
import json
from typing import Dict
from common.http_utils import send_request
from common.common_utils import settings
from common.log_utils import log

# 配置信息

def get_config(group: str, data_id: str, nacos_base_url: str) -> Dict:
    query_params = {
        "dataId": data_id,
        "group": group
    }
    
    config_api = "/nacos/v2/cs/config"
    url = urljoin(nacos_base_url, config_api)
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    try:
        resp = send_request("GET", url=url, params=query_params, headers=headers)
        
        if resp.ok:
            response_data = resp.json()
            if "data" in response_data:
                data = response_data["data"]
                secret_config = json.loads(data)
                log.debug(f"获取的配置文件：{secret_config}")
                return secret_config
            else:
                log.error(f"Nacos返回的数据格式不正确: {response_data}")
                return {}
        else:
            log.error(f"Nacos请求失败: {resp.status_code}, {resp.content}")
            log.info("无法获取 nacos 配置")
            return {}
    except Exception as e:
        log.error(f"获取Nacos配置时发生异常: {str(e)}")
        return {}
    
    
if __name__ == "__main__":
    secret_config = get_config(settings.config_group, settings.secret_config, settings.nacos_base_url)
    print(secret_config)
    #for item in secret_config['mysql']:
    #    print(item['host'])
    