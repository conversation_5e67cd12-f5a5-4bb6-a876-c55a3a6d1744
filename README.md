# dds

提取dds.iata.com网站数据

### 多地区数据支持

项目支持多个亚太地区的航空数据提取：

- 中国大陆、港澳台地区
- 日本、韩国等

### 配置化报表系统

拥有20+个报表配置模板，支持灵活的数据提取需求。


### 设计模式和架构风格

- **分层架构**：采用经典的三层架构模式
    -  `service/`  - 业务逻辑层
    -  `persistent/` - 数据持久化层
    -  `model/` - 数据模型层
- **服务导向**：核心功能封装在`DDSService`和`WebService`中
- **配置驱动**：大量JSON配置文件支持不同报表类型


## 主要技术
```
selenium          # Web自动化
pycryptodome      # 加密解密
requests          # HTTP客户端
sqlalchemy        # ORM框架
pymysql           # MySQL驱动
pytest            # 测试框架
duckdb            # 分析型数据库
```

## 模块组织结构

```
├── service/          # 业务服务层
│   ├── dds_service.py    # DDS API服务
│   └── web_service.py    # Web自动化服务
├── persistent/       # 数据持久化
│   └── tokens_mapper.py  # Token管理
├── model/           # 数据模型
│   └── job.py           # 任务模型
├── config/          # 配置文件
│   ├── db.sql           # 数据库结构
│   └── report_sample_*.json  # 报表配置模板
└── test/            # 测试代码
```

## 核心业务函数

⭐⭐⭐ 核心业务函数
1. main() - 主入口函数，协调整个ETL流程
2. DDSService.submit() - 提交报表任务到IATA API
3. DDSService.get_job_status() - 查询任务执行状态
4. DDSService.download_job() - 下载生成的报表文件
5. ParseService.parse_csv() - CSV数据解析和清洗
6. ParseService.write2doris() - 数据写入Doris数据仓库
7. WebService.login() - 获取认证Token

⭐⭐ 重要辅助函数
1. get_config_by_file() - 生成报表配置
2. TokensMapper.add() - Token持久化管理
3. stream_load() - Doris数据加载工具

⭐ 工具函数
1. get_saved_configuration_name() - 生成任务名称
2. get_csv_file_name() - 生成文件名
3. encode_to_base64()/decode_from_base64() - 编码工具


## 调用链路

Mermaid流程图

```mermaid
graph TD
    A[main函数启动] --> B{检查Token有效性}
    B -->|Token失效| C[WebService.login]
    B -->|Token有效| D[创建Job对象]
    
    C --> C1[driver.get打开页面]
    C1 --> C2[执行登录操作链]
    C2 --> C3[获取Session Token]
    C3 --> C4[TokensMapper.add保存]
    C4 --> C5[清理历史Token]
    C5 --> D
    
    D --> E[DDSService.submit提交任务]
    E --> F[轮询状态检查]
    F --> G{任务是否完成?}
    G -->|否| H[sleep等待]
    H --> F
    G -->|是| I[DDSService.download_job]
    
    I --> J[ParseService.parse_csv]
    J --> J1[读取CSV文件]
    J1 --> J2[数据清洗去除头尾]
    J2 --> J3[DuckDB SQL处理]
    J3 --> J4[生成解析后文件]
    
    J4 --> K[ParseService.write2doris]
    K --> K1[获取Nacos配置]
    K1 --> K2[调用stream_load]
    K2 --> L[流程结束]
    
    style A fill:#e1f5fe
    style F fill:#fff3e0
    style G fill:#f3e5f5
    style L fill:#e8f5e8
```

ASCII调用链路图
```
main()
├── DDSService.login_invalid() ──┐
│                                │
├── WebService.login() ←─────────┘
│   ├── driver.get()
│   ├── run(login_sttr_loc)
│   │   └── handler_chain() [循环]
│   ├── execute_script()
│   ├── TokensMapper.add()
│   ├── TokensMapper.update_history_token()
│   └── __close__()
│
├── Job() 对象创建
├── get_config_by_file()
├── get_csv_file_name()
├── get_saved_configuration_name()
│
├── DDSService.submit()
├── DDSService.get_job_status() [轮询循环]
├── DDSService.download_job()
│
├── ParseService.parse_csv()
│   ├── pandas.read_csv()
│   ├── DuckDB SQL处理
│   └── 文件输出
│
└── ParseService.write2doris()
    ├── get_config() [Nacos]
    └── stream_load() [Doris工具]
```

## 参数含义


1. 调度平台的配置文件

```
"max_polling_time_out" # 防止轮训任务状态死锁，添加超时时间，单位时秒
"submit_url" # DDS提交任务接口
"get_job_report_url" # DDS获取任务状态接口
"download_url" #  DDS下载文件接口
"project_base_path" # 项目在服务器上的地址，用于拼接downloads目录使用
"downloads_path" # 项目下载文件下载的subpath
"n_token_flash_second" # 登录Token失效时间，默认是1小时1小时后会重新进行selenium登录
"loop_interval" # DDS检查任务轮训间隔，默认是5S
"job_success" # 任务成功的状态
"login_max_retry_count" # selenium登录如果失败重试的次数
"login_retry_interval_second" # selenium登录如果失败重试等待间隔，默认是5s
"chrome_runtime_mem" # selenium启动无头浏览器运存
"selenium_operate_max_timeout_second" # selenium操作最大超时时间
"selenium_step2step_interval_second" # selenium每一步操作之间等待间隔，默认是1s
"remote_debugging_port" # 用于启动在linux上的浏览器监听端口
"doris_table_name" # 落表的名称, 用于streamload to doris
"doris_table_columns" # 落表的字段, 用于streamload to doris

```

2. nacos平台的配置参数
```
mysql # 配置python的相关信息，host、port、user、password、database
doris # 配置doris相关信息
dds_url # 配置dds登录地址
dds_username # 配置dds登录用户名
dds_password # 配置dds登录密码
dds_api_url # 配置DDS
```

## 部署免密说明
1. 切换到ds用户，测试环境是**mytenant**，生产是自行调整
```
su - mytenant
```
2. 尝试ssh到目标服务器root@***********

```
ssh root@***********
```
如果受阻，进行下一步配置，没有就说明已经免密了
3. 将DS机器上的id_rsa.pub的值，放在root@*********** 的 ~/.ssh/authorized_keys
```
# 登录DS ************-T-D-ds05（略）
# 切换 用户
su - mytenant
# 查看id_rsa.pub的值
cat ~/.ssh/id_rsa.pub
# 登录调度机 ***********-trace01 默认是在root用户
# 将上面id_rsa的值放在当前机器的~/.ssh/known_hosts
vim ~/.ssh/known_hosts
```

上面操作完后，进行验证

## 核心提取的参数

```
中国大陆 : 
{
    "name": "People's Republic of China | Sub Region",
    "code": "CN",
    "type": "country"
}

中国港澳台
[
    {
        "name": "Chinese Taipei | Sub Region",
        "code": "TW",
        "type": "country"
    },
    {
        "name": "Hong Kong (SAR), China | Sub Region",
        "code": "HK",
        "type": "country"
    },
    {
        "name": "Macao (SAR), China | Sub Region",
        "code": "MO",
        "type": "country"
    }
]

日本
{
    "code": "JP",
    "name": "Japan | Sub Region",
    "type": "country"
}

韩国
{
    "code": "KR",
    "name": "Republic of Korea | Sub Region",
    "type": "country"
}

东南亚（新加坡、马来西亚、泰国、越南、印尼、菲律宾、柬埔寨）
[
    {
        "code": "SG",
        "name": "Singapore | Sub Region",
        "type": "country"
    },
    {
        "code": "MY",
        "name": "Malaysia | Sub Region",
        "type": "country"
    },
    {
        "code": "TH",
        "name": "Thailand | Sub Region",
        "type": "country"
    },
    {
        "code": "ID",
        "name": "Indonesia | Sub Region",
        "type": "country"
    },
    {
        "code":"VN",
        "name":"Viet Nam | Sub Region",
        "type":"country"
    },
    {
        "code": "PH",
        "name": "Philippines | Sub Region",
        "type": "country"
    },
    {
        "code":"KH",
        "name":"Cambodia | Sub Region",
        "type":"country"
    }
]

欧洲
{
    "code":"",
    "name":"Europe",
    "type":"region"
}
澳洲
{
    "code": "",
    "name": "Australasia",
    "type": "region"
}

除亚欧澳之外

{
    "code": "",
    "name": "Australasia",
    "type": "region"
},
{
    "code": "",
    "name": "Asia",
    "type": "region"
},
{
    "code":"",
    "name":"Europe",
    "type":"region"
}


其他国家

{
    "code": "",
    "name": "Asia",
    "type": "region"
}
[
    {
        "name": "People's Republic of China | Sub Region",
        "code": "CN",
        "type": "country"
    },
    {
        "name": "Chinese Taipei | Sub Region",
        "code": "TW",
        "type": "country"
    },
    {
        "name": "Hong Kong (SAR), China | Sub Region",
        "code": "HK",
        "type": "country"
    },
    {
        "name": "Macao (SAR), China | Sub Region",
        "code": "MO",
        "type": "country"
    },
    {
        "code": "JP",
        "name": "Japan | Sub Region",
        "type": "country"
    },
    {
        "code": "KR",
        "name": "Republic of Korea | Sub Region",
        "type": "country"
    },
    {
        "code": "SG",
        "name": "Singapore | Sub Region",
        "type": "country"
    },
    {
        "code": "MY",
        "name": "Malaysia | Sub Region",
        "type": "country"
    },
    {
        "code": "TH",
        "name": "Thailand | Sub Region",
        "type": "country"
    },
    {
        "code": "ID",
        "name": "Indonesia | Sub Region",
        "type": "country"
    },
    {
        "code":"VN",
        "name":"Viet Nam | Sub Region",
        "type":"country"
    },
    {
        "code": "PH",
        "name": "Philippines | Sub Region",
        "type": "country"
    },
    {
        "code":"KH",
        "name":"Cambodia | Sub Region",
        "type":"country"
    }
]
```