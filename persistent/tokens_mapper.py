from persistent.core.base_mapper import BaseMapper
from model.tokens import Tokens
from typing import Optional
import datetime

class TokensMapper(BaseMapper[Tokens]):
    def __init__(self):
        super().__init__(Tokens)
    
    def get_by_token(self, token_str: str) -> Optional[Tokens]:
        """根据token字符串查找token"""
        with self.get_session() as session:
            return session.query(self.model_class).filter(
                self.model_class.token == token_str
            ).first()

    
    def update_history_token(self, dtime):
        """ 将历史Token逻辑删除, 1小时以前的Token """
        
        stime = dtime - datetime.timedelta(hours=1)
        with self.get_session() as session:
            session.query(self.model_class).filter(
                self.model_class.insert_time < stime
            ).update({
                self.model_class.is_delete: 1
            })
            session.commit()


    def get_end_tokens(self, n_token_flash_second : int) -> Optional[Tokens]:
        with self.get_session() as session:
            return session.query(self.model_class).filter(
                self.model_class.insert_time >= datetime.datetime.now() - datetime.timedelta(seconds=n_token_flash_second),
                self.model_class.is_delete != 1
            ).first()
        
       
        

