# pip install sqlalchemy
from typing import TypeVar, Generic, List, Optional, Type
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import DeclarativeMeta
from common.common_utils import settings
from common.nacos_utils import get_config

# 定义泛型类型变量，约束为SQLAlchemy模型
T = TypeVar('T', bound='DeclarativeMeta')

class BaseMapper(Generic[T]):
    """
    通用数据访问层基类，使用泛型支持不同的实体类型
    """
    
    _client = None
    _session_factory = None
    
    def __init__(self, model_class: Type[T]):
        """
        初始化BaseMapper
        
        Args:
            model_class: SQLAlchemy模型类
        """
        self.model_class = model_class
        self._init_database()
    
    def _init_database(self):
        """初始化数据库连接"""
        if BaseMapper._client is None:
            nacos_config = get_config(
                settings.config_group, 
                settings.secret_config, 
                settings.nacos_base_url
            )
            
            mysql_config = nacos_config['mysql'][0]
            host = mysql_config['host']
            port = mysql_config['port']
            user = mysql_config['user']
            password = mysql_config['password']
            database = mysql_config['database']
            
            connection_string = f'mysql+pymysql://{user}:{password}@{host}:{port}/{database}'
            BaseMapper._client = create_engine(connection_string)
            BaseMapper._session_factory = sessionmaker(bind=BaseMapper._client)
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return BaseMapper._session_factory()
    
    def add(self, entity: T) -> T:
        """
        添加单个实体
        
        Args:
            entity: 要添加的实体对象
            
        Returns:
            添加后的实体对象
        """
        with self.get_session() as session:
            session.add(entity)
            session.commit()
            session.refresh(entity)
            return entity
    
    def add_batch(self, entities: List[T]) -> List[T]:
        """
        批量添加实体
        
        Args:
            entities: 要添加的实体列表
            
        Returns:
            添加后的实体列表
        """
        with self.get_session() as session:
            session.add_all(entities)
            session.commit()
            for entity in entities:
                session.refresh(entity)
            return entities
    
    def get_by_id(self, entity_id: int) -> Optional[T]:
        """
        根据ID获取实体
        
        Args:
            entity_id: 实体ID
            
        Returns:
            找到的实体对象，如果不存在则返回None
        """
        with self.get_session() as session:
            return session.query(self.model_class).filter(
                self.model_class.id == entity_id
            ).first()
    
    def get_all(self) -> List[T]:
        """
        获取所有实体
        
        Returns:
            所有实体的列表
        """
        with self.get_session() as session:
            return session.query(self.model_class).all()
    
    def get_latest(self) -> Optional[T]:
        """
        获取最新的实体（按ID倒序）
        
        Returns:
            最新的实体对象，如果不存在则返回None
        """
        with self.get_session() as session:
            return session.query(self.model_class).order_by(
                self.model_class.id.desc()
            ).first()
    
    def update(self, entity: T) -> T:
        """
        更新实体
        
        Args:
            entity: 要更新的实体对象
            
        Returns:
            更新后的实体对象
        """
        with self.get_session() as session:
            session.merge(entity)
            session.commit()
            return entity
    
    def update_by_id(self, id: int, entity: T) -> T:
        """
        更新实体
        
        Args:
            entity: 要更新的实体对象
            
        Returns:
            更新后的实体对象
        """
        with self.get_session() as session:
            session.query(self.model_class).filter(
                self.model_class.id == id
            ).update(entity.to_dict())
            session.commit()
            return session.query(self.model_class).filter(
                self.model_class.id == id
            ).first()
            
    
    def delete_by_id(self, entity_id: int) -> bool:
        """
        根据ID删除实体
        
        Args:
            entity_id: 要删除的实体ID
            
        Returns:
            删除是否成功
        """
        with self.get_session() as session:
            entity = session.query(self.model_class).filter(
                self.model_class.id == entity_id
            ).first()
            
            if entity:
                session.delete(entity)
                session.commit()
                return True
            return False
    
    def delete(self, entity: T) -> bool:
        """
        删除实体
        
        Args:
            entity: 要删除的实体对象
            
        Returns:
            删除是否成功
        """
        with self.get_session() as session:
            session.delete(entity)
            session.commit()
            return True
    
    def count(self) -> int:
        """
        获取实体总数
        
        Returns:
            实体总数
        """
        with self.get_session() as session:
            return session.query(self.model_class).count()