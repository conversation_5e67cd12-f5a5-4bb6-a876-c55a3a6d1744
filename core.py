import requests
import json

requests.packages.urllib3.disable_warnings()

# ################ ################ ################ ################ ################ ################ ################ ################ ################ ############### 登录

# url = "https://portal.iata.org/s/sfsites/aura"

# params = {
#   'r': "2",
#   'aura.ApexAction.execute': "1"
# }

# payload = "message=%7B%22actions%22%3A%5B%7B%22id%22%3A%2258%3Ba%22%2C%22descriptor%22%3A%22aura%3A%2F%2FApexActionController%2FACTION%24execute%22%2C%22callingDescriptor%22%3A%22UNKNOWN%22%2C%22params%22%3A%7B%22namespace%22%3A%22%22%2C%22classname%22%3A%22PortalLoginCtrl%22%2C%22method%22%3A%22login%22%2C%22params%22%3A%7B%22username%22%3A%22liusicong%40juneyaoair.com%22%2C%22password%22%3A%22juneyaoPW%40IATA06%22%2C%22landingPage%22%3A%22undefined%22%2C%22params%22%3A%7B%22language%22%3A%22en_US%22%7D%7D%2C%22cacheable%22%3Afalse%2C%22isContinuation%22%3Afalse%7D%7D%5D%7D&aura.context=%7B%22mode%22%3A%22PROD%22%2C%22fwuid%22%3A%22VXlnM1FET1BLV0NVVUNZMW9MNmU3UWdLNVAwNUkzRVNnOFJ1eVRYdHBvVVExMi42MjkxNDU2LjE2Nzc3MjE2%22%2C%22app%22%3A%22siteforce%3AloginApp2%22%2C%22loaded%22%3A%7B%22APPLICATION%40markup%3A%2F%2Fsiteforce%3AloginApp2%22%3A%221218_urw0gFog8h0kox9sAhJinQ%22%7D%2C%22dn%22%3A%5B%5D%2C%22globals%22%3A%7B%7D%2C%22uad%22%3Atrue%7D&aura.pageURI=%2Fs%2Flogin%2F%3Flanguage%3Den_US&aura.token=null"

# headers = {
#   'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
#   'Accept-Encoding': "gzip, deflate, br, zstd",
#   'sec-ch-ua': "\"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Microsoft Edge\";v=\"128\"",
#   'x-sfdc-lds-endpoints': "ApexActionController.execute:PortalLoginCtrl.login",
#   'x-sfdc-page-scope-id': "e452b037-d1ee-4602-a2ff-bba451129e4f",
#   'sec-ch-ua-mobile': "?0",
#   'sec-ch-ua-arch': "\"x86\"",
#   'content-type': "application/x-www-form-urlencoded; charset=UTF-8",
#   'sec-ch-ua-full-version': "\"128.0.2739.67\"",
#   'sec-ch-ua-platform-version': "\"10.15.4\"",
#   'sec-ch-ua-full-version-list': "\"Chromium\";v=\"128.0.6613.120\", \"Not;A=Brand\";v=\"********\", \"Microsoft Edge\";v=\"128.0.2739.67\"",
#   'sec-ch-ua-bitness': "\"64\"",
#   'sec-ch-ua-model': "\"\"",
#   'sec-ch-ua-platform': "\"macOS\"",
#   'origin': "https://portal.iata.org",
#   'sec-fetch-site': "same-origin",
#   'sec-fetch-mode': "cors",
#   'sec-fetch-dest': "empty",
#   'referer': "https://portal.iata.org/s/login/?language=en_US",
#   'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6,en-GB;q=0.5",
#   'priority': "u=1, i",
#   'Cookie': "renderCtx=%7B%22pageId%22%3A%2243cd01cb-d8be-47c7-89c2-43caa1c83a49%22%2C%22schema%22%3A%22Published%22%2C%22viewType%22%3A%22Published%22%2C%22brandingSetId%22%3A%220bef5b57-3b4d-47b8-b6b9-b2ee0240183e%22%2C%22audienceIds%22%3A%226Au5J00000001rz%22%7D; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1; _ga=GA1.1.705992944.1750310476; _gcl_au=1.1.1339060157.1750310476; _ugeuid=4928edef-9189-48f7-906d-27df799884a5; oinfo=c3RhdHVzPUFDVElWRSZ0eXBlPTYmb2lkPTAwRDIwMDAwMDAwMDhURg==; autocomplete=0; oid=00D2000000008TF; cf_clearance=AtS0DWiXasjHvtP749EAX2wf6s446lgIh.fiD7.UVzk-1750323452-1.2.1.1-z0C8Su5Nw7P4aXM6FkHsaA6sjd7rkTGCEKSY4p6qN5Pp8NRLRbdUUhTUj3XhWzwxC57vIBKWX7RHksD6SHjEpQlPGFQ3HoC4ztFzhkF5eQfbwoufG6oZu7yVd3BqA3.fPOHt4eIHzfIEUNXYx5viJHYJuVcXC0s.olsajijAaQPMHiXGNNRqHInugxYtNHJoMtpoOEBcDPh.4SxPrOud90o3SxlLM1QhYh7uSReD7WpkldoP0zyHWMwTBNUredsU5dCKk99pMJYFXxuNrMMF0DWA3nxzrS4lQqV2Vnju325yl0p2frVjpF4U8d2Q1ASWeXIUnPX9.YKtCamUpl8eJXTtROhuyAEyq5x8y9hQXpjFkFUUlqfcuvHCJS5Yn2gz; LSKey-c$userguiding_acc_categ=Passenger and Cargo; LSKey-c$userguiding_acc_sector=Airline; LSKey-c$userguiding_iso-code=CN; LSKey-c$userguiding_user-status=Pending Approval; LSKey-c$userguiding_job-function=IT; perf_dv6Tr4n=1; _cfuvid=toFG6U8FaMYM5T679ft77VvZfJJFkvFAghLjkE7Q6.o-1750731311368-0.0.1.1-604800000; _ga_PLLG1EY0X0=GS2.1.s1750727403$o4$g1$t1750731328$j46$l0$h790339248; LSKey-c$portalLoginReferrer=https://portal.iata.org/secur/logout.jsp?retUrl=/s/login"
# }

# response = requests.post(url, params=params, data=payload, headers=headers, verify=False)

# print(response.text)



# ################ ################ ################ ################ ################ ################ ################ ################ ############### 获取文件列表

# import requests

# url = "https://api.dds.iata.org/system-management/management/systems/user-conveniences/report-histories"

# params = {
#   'type': "all",
#   'pageSize': "5",
#   'pageNumber': "1"
# }

# headers = {
#   'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
#   'Accept': "application/json, text/plain, */*",
#   'Accept-Encoding': "gzip, deflate, br, zstd",
#   'sec-ch-ua': "\"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Microsoft Edge\";v=\"128\"",
#   'sec-ch-ua-mobile': "?0",
#   'authorization': "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ik1tRV95d2dOa1hpa0phZlF2emFjYThWRENPbjZQbXlINThxN09lOEVreDgiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DJjddqLv187Ro2CzWQf1BPz54eWEFt67XvdHc_Ej7EmmIJYY-yeNJ28r5_1l08MMC2A7qdZKasdSBktlt2ScG2RvtFWKfW2egZ5ByQnYam2adI2CsfOAvO0UyvFWZ3j3drsOBJKjnbD_lkg_W14ZABgHPhWEyO_RvLjg1NeVOdDxMDd_mSyl3-pvZYn09JRC1F3wZHHwMuUviSh13_-yLrg_N8Fqomz8tunVPdVkHyQbDMSCqEizk2RNNPN0zTvw58rLB4w6h3_HGkZmDjBnUSRMPSW2zcGXWFBbJj1Z3wkvx-tmKLkIK8e93QlztpwYgCP_qhpIZdLw6gSgPp-Dgg",
#   'sec-ch-ua-platform': "\"macOS\"",
#   'origin': "https://dds.iata.org",
#   'sec-fetch-site': "same-site",
#   'sec-fetch-mode': "cors",
#   'sec-fetch-dest': "empty",
#   'referer': "https://dds.iata.org/",
#   'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6,en-GB;q=0.5",
#   'priority': "u=1, i"
# }

# response = requests.get(url, params=params, headers=headers, verify=False)

# print(response.text)

# ################ ################ ################ ################ ################ ################ ################ ################ ############### 提交任务
# import requests
# import json



# for x in range(1, 10):
# #   print(x)

# url = "https://api.dds.iata.org/dds-reports/api/request-report"

# payload = json.dumps({
# "userId": "68215f49d624fd55d2fd3ee3",
# "reportNames": "ddsTicketingPivot",
# "savedConfiguration": "{\"pivotTableLayout\":\"staticLayout\",\"reportLayout\":{\"column\":{},\"row\":{\"tripFields\":{\"serviceClass\":true,\"rbkd\":true,\"marketingAirline\":{\"perUserPreference\":false,\"code\":true,\"name\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false},\"allMarketingAirlines\":false,\"operatingAirline\":{\"perUserPreference\":false,\"code\":true,\"name\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false},\"allOperatingAirlines\":false,\"tripMarket\":true,\"domIntTrip\":false,\"origin\":{\"perUserPreference\":false,\"code\":true,\"name\":false,\"city\":true,\"state\":false,\"countryCode\":true,\"country\":true,\"region\":true,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false,\"regionOnlyNonOverlapping\":false,\"regionNonOverlappingOverlapping\":false},\"connectPoints\":false,\"destination\":{\"perUserPreference\":false,\"code\":true,\"name\":false,\"city\":true,\"state\":false,\"countryCode\":true,\"country\":true,\"region\":true,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false,\"regionOnlyNonOverlapping\":false,\"regionNonOverlappingOverlapping\":false},\"connectNonStopIndicator\":true,\"partnershipIndicator\":false,\"fareFlag\":false,\"daysSoldPriorToTravel\":true,\"nightsStayed\":false,\"duration\":false},\"segmentFields\":{\"fareBasis\":false,\"couponNumber\":false,\"serviceClass\":false,\"rbkd\":false,\"marketingAirline\":{\"perUserPreference\":false,\"code\":false,\"name\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false},\"operatingAirline\":{\"perUserPreference\":false,\"code\":false,\"name\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false},\"origin\":{\"perUserPreference\":false,\"code\":false,\"name\":false,\"city\":false,\"state\":false,\"countryCode\":false,\"country\":false,\"region\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false,\"regionOnlyNonOverlapping\":false,\"regionNonOverlappingOverlapping\":false},\"destination\":{\"perUserPreference\":false,\"code\":false,\"name\":false,\"city\":false,\"state\":false,\"countryCode\":false,\"country\":false,\"region\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false,\"regionOnlyNonOverlapping\":false,\"regionNonOverlappingOverlapping\":false},\"marketingFlightNumber\":false,\"operatingFlightNumber\":false,\"equipment\":false,\"localDepartureTime\":{\"departureTime\":false,\"hourOfDeparture\":false,\"departureTime3Hr\":false,\"departureTime6Hr\":false},\"localArrivalTime\":{\"arrivalTime\":false,\"arrivalDate\":false},\"utcDepartureTimeDate\":false,\"utcArrivalTimeDate\":false,\"stopoverFlag\":false,\"localVsBehindBeyond\":false},\"ticketFields\":{\"visitor\":false,\"inNdc\":false,\"cdNdc\":false,\"ticketType\":true,\"travelAgency\":{\"agencyNumber\":true,\"agencyName\":true,\"tradingName\":true,\"agencyType\":true,\"address\":false,\"email\":false,\"phoneNumber\":false,\"city\":true,\"state\":false,\"countryCode\":true,\"country\":true,\"region\":true,\"postalCode\":false,\"nationalOfficeId\":false,\"nationalOfficeName\":true,\"globalOfficeId\":false,\"globalOfficeName\":true,\"gboOnlyNonOverlapping\":false,\"gboNonOverlappingOverlapping\":false,\"gnoOnlyNonOverlapping\":false,\"gnoNonOverlappingOverlapping\":false,\"ggoOnlyNonOverlapping\":false,\"ggoNonOverlappingOverlapping\":false},\"countryOfSale\":{\"code\":true,\"name\":true,\"rgOnlyNonOverlapping\":false,\"rgNonOverlappingOverlapping\":false},\"dataSource\":true,\"gds\":true,\"reportingSystemIndicator\":false,\"ticketingAirline\":{\"perUserPreference\":false,\"code\":true,\"name\":true,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false},\"pooAirline\":{\"perUserPreference\":false,\"code\":true,\"name\":true,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false},\"pooAirport\":{\"perUserPreference\":false,\"code\":true,\"name\":false,\"city\":false,\"state\":false,\"country\":false,\"region\":false,\"onlyNonOverlapping\":false,\"nonOverlappingOverlapping\":false,\"regionOnlyNonOverlapping\":false,\"regionNonOverlappingOverlapping\":false},\"allMarketingAirlines\":false,\"allOperatingAirlines\":false,\"allTripOds\":false,\"allCoupons\":false,\"eTicketFlag\":false,\"domIntTkt\":false,\"partnershipIndicator\":false,\"paymentCurrency\":false,\"paymentExchangeRates\":{\"usd\":false,\"eur\":false,\"gbp\":false,\"cny\":false,\"jpy\":false,\"rub\":false,\"cad\":false,\"brl\":false,\"aud\":false,\"inr\":false,\"krw\":false},\"ticketDocumentNumber\":false,\"distributionChannel\":true,\"detailsForExchangesRefunds\":{\"transactionCode\":true,\"originalIssueInformation\":false,\"previousIssuesInformation\":false,\"exchangeRefundedCouponNumbers\":false},\"populatedForTicketsYourAirlinePlated\":{\"pnrCode\":false,\"tourCode\":false}},\"datePeriodFields\":{\"matchInput\":false,\"ticketPurchasePeriod\":{\"purchaseDate\":true,\"purchaseMonthMMMyyyy\":false,\"purchaseMonthMM\":false,\"purchaseQuarterQyyyy\":false,\"purchaseQuaterQ\":false,\"purchaseYear\":false},\"tripPeriod\":{\"date\":true,\"dateOfTheWeek\":false,\"numberOfTheWeek\":false,\"monthMMMyyyy\":false,\"monthMM\":false,\"quarterQyyyy\":false,\"quarterQ\":false,\"year\":false},\"segmentPeriod\":{\"date\":false,\"dateOfTheWeek\":false,\"numberOfTheWeek\":false,\"monthMMMyyyy\":false,\"monthMM\":false,\"quarterQyyyy\":false,\"quarterQ\":false,\"year\":false},\"ticketPeriod\":{\"date\":false,\"dateOfTheWeek\":false,\"numberOfTheWeek\":false,\"monthMMMyyyy\":false,\"monthMM\":false,\"quarterQyyyy\":false,\"quarterQ\":false,\"year\":false}}},\"data\":{\"count\":false,\"pax\":true,\"paxSharePct\":false,\"loyaltyPax\":false,\"blendedFareBreakdown\":false,\"revenueKpi\":{\"blendedAverageFare\":true,\"actualFare\":false,\"industryFare\":false,\"blendedRevenueCe\":true,\"revenueShare\":false,\"actualRevenue\":false,\"industryRevenue\":false,\"blendedYield\":false,\"actualYield\":false,\"industryYield\":false,\"loyaltyRevenue\":false},\"paymentKpi\":{\"blendedPaymentAmount\":true,\"actualPaymentAmount\":false,\"industryPaymentAmount\":false,\"blendedPayment\":false,\"actualPayment\":false,\"industryPayment\":false,\"blendedPaymentAmountYield\":false,\"actualPaymentAmountYield\":false,\"industryPaymentAmountYield\":false},\"rpk\":false,\"odDistance\":true,\"adjustedFareKpi\":{\"adjustedFare\":false,\"adjustedRevenue\":false,\"adjustedYield\":false,\"adjustedPaymentAmount\":false,\"adjustedPayment\":false,\"adjustedPaymentAmountYield\":false},\"valuesPopulatedForTicketsYourAirlinePlated\":{\"netRevenue\":{\"amNetRevenue\":false,\"avgAmNetRevenue\":false},\"commission\":{\"amCommission\":false,\"avgCommission\":false},\"supplementaryAmount\":{\"amSupplementary\":false,\"avgSupplementary\":false},\"taxesAndFees\":{\"totalTaxesAndFees\":false,\"averageTaxesAndFees\":false,\"totalPenaltyAmount\":false,\"averagePenaltyAmount\":false,\"totalYrAmount\":false,\"averageYrAmount\":false,\"totalYqAmount\":false,\"averageYqAmount\":false}}},\"dateChipType\":\"dateInColumns\"},\"general\":{\"travelPeriod\":{\"dateTypePivot\":\"trip\",\"layout\":\"column\",\"timePeriod\":\"absolute\",\"periodType\":{\"singleMonth\":\"2401\"}},\"purchasePeriod\":{\"layout\":\"column\",\"timePeriod\":\"absolute\",\"periodType\":{}},\"airlineDetails\":{\"excludeTicketing\":false,\"excludePooMarketing\":false,\"ticketing\":[],\"pooMarketing\":[]},\"otherDetails\":{\"directionality\":\"bidirectional\",\"dataSource\":[],\"countryOfSale\":[],\"poo\":[],\"excludePoo\":false},\"agencyDetails\":{\"excludeCategory\":{\"categoryType\":\"\",\"category\":[]},\"chipType\":\"branchOffice\",\"categoryType\":\"city\",\"category\":[],\"name\":[],\"excludeName\":[]},\"yearToDateSnapshot\":false},\"ticket\":{\"itineraryDetails\":{\"excludeAnySegmentAirport\":false,\"anySegmentAirport\":[]},\"distribution\":{\"distributionChipType\":\"both\",\"distributionChannel\":[],\"gds\":[]},\"airlineDetails\":{\"excludeMarketing\":false,\"excludeOperating\":false,\"marketing\":[],\"operating\":[]},\"otherDetails\":{\"pnrCode\":{\"type\":\"contains\",\"code\":\"\"},\"tourCode\":{\"type\":\"contains\",\"code\":\"\"},\"tripType\":\"all\",\"partnershipType\":[],\"eTicketFlag\":\"\",\"transactions\":[],\"ticketNumber\":\"\"}},\"trip\":{\"tripDetails\":{\"tripType\":\"all\",\"chipsType\":\"all\",\"pairs\":{\"pair\":\"\",\"excludePair\":\"\"},\"originDestination\":{\"origin\":[{\"name\":\"Korean | Sub Region\",\"code\":\"KR\",\"type\":\"country\"}],\"destination\":[{\"name\":\"People's Republic of China | Sub Region\",\"code\":\"CN\",\"type\":\"country\"}],\"exclude\":{\"origin\":[],\"destination\":[]}},\"tripConnections\":{\"any\":true,\"first\":{\"code\":[],\"exclude\":false},\"second\":{\"code\":[],\"exclude\":false},\"third\":{\"code\":[],\"exclude\":false},\"fourth\":{\"code\":[],\"exclude\":false},\"fifth\":{\"code\":[],\"exclude\":false}}},\"airlineDetails\":{\"excludeMarketing\":false,\"excludeOperating\":false,\"marketingOrder\":{\"any\":true,\"first\":{\"code\":[],\"exclude\":false},\"second\":{\"code\":[],\"exclude\":false},\"third\":{\"code\":[],\"exclude\":false},\"fourth\":{\"code\":[],\"exclude\":false},\"fifth\":{\"code\":[],\"exclude\":false},\"six\":{\"code\":[],\"exclude\":false}},\"operatingOrder\":{\"any\":true,\"first\":{\"code\":[],\"exclude\":false},\"second\":{\"code\":[],\"exclude\":false},\"third\":{\"code\":[],\"exclude\":false},\"fourth\":{\"code\":[],\"exclude\":false},\"fifth\":{\"code\":[],\"exclude\":false},\"six\":{\"code\":[],\"exclude\":false}},\"marketingAirline\":[],\"operatingAirline\":[],\"serviceClass\":[]},\"otherDetails\":{\"rbkd\":[],\"partnershipType\":[]}},\"segment\":{\"segmentDetails\":{\"tripType\":\"all\",\"chipsType\":\"all\",\"originDestination\":{\"origin\":[],\"destination\":[],\"exclude\":{\"origin\":[],\"destination\":[]}}},\"airlineDetails\":{\"marketingAirline\":[],\"excludeMarketing\":false,\"operatingAirline\":[],\"excludeOperating\":false,\"marketingFlightNumber\":\"\",\"operatingFlightNumber\":\"\",\"serviceClass\":[]},\"otherDetails\":{\"equipment\":\"\",\"rbkd\":[],\"segDepartureStartTime\":\"\",\"segDepartureEndTime\":\"\",\"fareBasis\":{\"type\":\"contains\",\"code\":\"\"}}},\"travelPeriodDisplay\":\"February 2024\",\"pivot\":{\"column\":[],\"row\":[\"tktPurchaseDate\",\"tripDate\",\"deTicketTripType\",\"travelAgencyNumber\",\"travelAgencyName\",\"travelTradingName\",\"travelAgencyType\",\"travelAgencyCity\",\"travelAgencyCdCountry\",\"travelAgencyCountry\",\"travelAgencyRegion\",\"travelAgencyNationalofficename\",\"travelAgencyGlobalofficename\",\"cdTicketingCountry\",\"cosName\",\"cdSource\",\"nmAirlineGdsOrganization\",\"cdTicketingAirline\",\"ticketingAirlineName\",\"cdPooAirline\",\"pooAirlineName\",\"cdPooAirport\",\"deDistributionChannel\",\"cdTransactionUnified\",\"cdOdDominantCabinClass\",\"cdOdRbkd\",\"cdDomMktAirline\",\"cdDomOptAirline\",\"cdTrueOd\",\"origCd\",\"tripOriginCity\",\"tripOriginCdCountryRgn\",\"tripOriginCountryRgn\",\"tripOriginRegionRgn\",\"destCd\",\"tripDestCity\",\"tripDestCdCountryRgn\",\"tripDestCountryRgn\",\"tripDestRegionRgn\",\"inNonStop\",\"nbOdDaysSoldPriorToTravel\"],\"data\":[\"pax\",\"blendedAverageFare\",\"blendedRevenueCe\",\"blendedPaymentAmount\",\"odDistance\"]}}",
# "mode": "runInBackground",
# "subscriptionType": "dds",
# "timeOut": 30000,
# "scheduledReport": {
#     "savedConfigurationName": "ZYH_Case1",
#     "id": "6853cae859f3511d14f9fc9a"
# },
# "exportAs": None
# })

# headers = {
# 'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
# 'Accept': "application/json, text/plain, */*",
# 'Accept-Encoding': "gzip, deflate, br, zstd",
# 'Content-Type': "application/json",
# 'sec-ch-ua': "\"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Microsoft Edge\";v=\"128\"",
# 'sec-ch-ua-mobile': "?0",
# 'authorization': "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ik1tRV95d2dOa1hpa0phZlF2emFjYThWRENPbjZQbXlINThxN09lOEVreDgiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U2xJzomgZrjfg5lkQOeH6jcZ0_qvSb8IW6KwE7FGqQ3KybYs00wkAgyKTxX_YWZL0o6mAhtneeZFIBs99J5geu38vKRu3Rx82xQxCqT2BUahUUjtFlHoOM9t-L1w3Wb2sMDh_QemXqxtqi9b-poF6WVdpB3XKv65d-XUq-4i5E-eeWcyMYyz_y6UPAA9DB_yyq5rLSO5QG1aSFlUf7lTc6IVUKHI195eO-qlruqi9eS9Q6FDL_q8WGgN2hubRInLzwtquGS8IMfp0kVDj_gREJJ34zRztKGk9JRI2pINkrG2t3AX54Z2BjTTWy8p0ejgLbaXQwo9E6UZBFqEO8gEwg",
# 'ngsw-bypass': "true",
# 'sec-ch-ua-platform': "\"macOS\"",
# 'origin': "https://dds.iata.org",
# 'sec-fetch-site': "same-site",
# 'sec-fetch-mode': "cors",
# 'sec-fetch-dest': "empty",
# 'referer': "https://dds.iata.org/",
# 'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6,en-GB;q=0.5",
# 'priority': "u=1, i"
# }

# response = requests.post(url, data=payload, headers=headers, verify=False)

# print(response.text)



# ################ ################ ################ ################ ################ ################ ################ ################ ############### 下载任务


# import requests

# url = "https://api.dds.iata.org/user-conveniences/api/user-conveniences/download/report-histories"

# params = {
#   'reportHistoryIds': "685a4137f834a111d67cc874"
# }

# headers = {
#   'User-Agent': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
#   'Accept': "application/json, text/plain, */*",
#   'Accept-Encoding': "gzip, deflate, br, zstd",
#   'sec-ch-ua': "\"Not-A.Brand\";v=\"99\", \"Chromium\";v=\"124\"",
#   'sec-ch-ua-mobile': "?0",
#   'authorization': "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ik1tRV95d2dOa1hpa0phZlF2emFjYThWRENPbjZQbXlINThxN09lOEVreDgiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DJjddqLv187Ro2CzWQf1BPz54eWEFt67XvdHc_Ej7EmmIJYY-yeNJ28r5_1l08MMC2A7qdZKasdSBktlt2ScG2RvtFWKfW2egZ5ByQnYam2adI2CsfOAvO0UyvFWZ3j3drsOBJKjnbD_lkg_W14ZABgHPhWEyO_RvLjg1NeVOdDxMDd_mSyl3-pvZYn09JRC1F3wZHHwMuUviSh13_-yLrg_N8Fqomz8tunVPdVkHyQbDMSCqEizk2RNNPN0zTvw58rLB4w6h3_HGkZmDjBnUSRMPSW2zcGXWFBbJj1Z3wkvx-tmKLkIK8e93QlztpwYgCP_qhpIZdLw6gSgPp-Dgg",
#   'sec-ch-ua-platform': "\"macOS\"",
#   'origin': "https://dds.iata.org",
#   'sec-fetch-site': "same-site",
#   'sec-fetch-mode': "cors",
#   'sec-fetch-dest': "empty",
#   'referer': "https://dds.iata.org/",
#   'accept-language': "zh-CN,zh;q=0.9",
#   'priority': "u=1, i"
# }

# response = requests.get(url, params=params, headers=headers, verify=False)

# print(response.text)


from datetime import datetime

original_time_str = "2025-07-18T03:11:37.682841487Z"

# 方法1：直接截断到6位微秒（推荐）
adjusted_time_str = original_time_str[:26] + "Z"  # 保留 .682841
dt = datetime.fromisoformat(adjusted_time_str.replace("Z", "+00:00"))

# 方法2：用字符串替换移除多余位数
# adjusted_time_str = original_time_str.replace("682841487", "682841") + "Z"

formatted_time = dt.strftime("%d %b %y, %H:%M:%S UTC")
print(formatted_time)  # 输出: 18 Jul 25, 03:11:37 UTC